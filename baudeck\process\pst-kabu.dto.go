package process

import (
	"gopkg.in/guregu/null.v4"
	"strconv"
)

type PstKabuKpi struct {
	Secondary null.Float `db:"secondary"`
	Tertiary  null.Float `db:"tertiary"`
}

type PstKabuData struct {
	MonthID   string      `db:"month_id"`
	Period    string      `db:"period"`
	AsofDate  string      `db:"asof_date"`
	Circle    null.String `db:"circle"`
	Region    null.String `db:"region"`
	Kabupaten null.String `db:"kabupaten"`
	KabuFlag  null.String `db:"flag"`
	Brand     string      `db:"brand"`
	PstKabuKpi
}

func (p PstKabuData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "circle", "region", "kabupaten", "flag", "brand", "secondary_mn", "tertiary_mn"}
}

func (p PstKabuData) GetRowValues() []string {
	secondary := ""
	if p.Secondary.Valid {
		secondary = strconv.FormatFloat(p.Secondary.ValueOrZero(), 'f', -1, 64)
	}

	tertiary := ""
	if p.Tertiary.Valid {
		tertiary = strconv.FormatFloat(p.Tertiary.ValueOrZero(), 'f', -1, 64)
	}

	return []string{p.MonthID, p.Period, p.AsofDate, p.Circle.String, p.Region.String, p.Kabupaten.String, p.KabuFlag.String, p.Brand, secondary, tertiary}
}

type RegionalPstKabuData struct {
	EntityType string
	EntityName string
	KabuCount  int
	MTD        *PstKabuKpi
	LMTD       *PstKabuKpi
	FM         map[string]*PstKabuKpi
}

type PstKabuReport struct {
	AsofDate        string
	FmList          []string
	CircleMap       map[string]*RegionalPstKabuData
	RegionalMap     map[string]*RegionalPstKabuData
	RegionalKabuMap map[string]map[string]*RegionalPstKabuData
	NationalData    *RegionalPstKabuData
	NationalKabuMap map[string]*RegionalPstKabuData
}
