package process

import (
	"context"
	"encoding/csv"
	"fmt"
	ctx "github.com/csee-pm/etl/shared/context"
	"github.com/csee-pm/etl/shared/utils"
	"io"
	"os"
	"path/filepath"
	"strings"
)

func GetKabuMap(c context.Context) (map[string]string, error) {
	logger := ctx.ExtractLogger(c)
	rootDir := ctx.ExtractRootDir(c)

	fPath := filepath.Join(rootDir, "kabu_mapping.csv")

	logger.Debug("reading kabu mapping data", "path", fPath)

	// check if file exists
	if !utils.FileExists(fPath) {
		return nil, fmt.Errorf("file not found: %s", fPath)
	}

	f, err := os.Open(fPath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read() // skip header
	kabuMap := make(map[string]string)
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, err
		}

		kabu := strings.TrimSpace(strings.ToUpper(record[0]))
		segment := strings.TrimSpace(strings.ToUpper(record[4]))
		kabuMap[kabu] = segment
	}

	return kabuMap, nil
}

func KabuMapFileExists(c context.Context) bool {
	rootDir := ctx.ExtractRootDir(c)
	fPath := filepath.Join(rootDir, "kabu_mapping.csv")
	return utils.FileExists(fPath)
}
