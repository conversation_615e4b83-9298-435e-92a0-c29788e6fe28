package process

import (
	"strconv"
	"time"

	"gopkg.in/guregu/null.v4"
)

type DseData struct {
	MonthID        string      `db:"month_id" csv:"month_id"`
	Period         string      `db:"period" csv:"period"`
	AsofDate       string      `db:"asof_date" csv:"asof_date"`
	Brand          string      `db:"brand" csv:"brand"`
	Circle         null.String `db:"circle" csv:"circle"`
	Region         null.String `db:"region" csv:"region"`
	Dse            string      `db:"dse" csv:"dse"`
	GrossAdds      int         `db:"ga" csv:"ga"`
	GASlab         string      `csv:"ga_slab"`
	NetSecondaryMn float64     `db:"net_secondary_mn" csv:"net_secondary_mn"`
	NetSecSlab     string      `csv:"net_sec_slab"`
	DayNum         int         `db:"day_no" csv:"day_no"`
}

func (d DseData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "brand", "circle", "region", "dse", "ga", "ga_slab", "net_secondary_mn", "net_sec_slab", "day_no"}
}

func (d DseData) GetRowValues() []string {
	return []string{d.MonthID, d.Period, d.AsofDate, d.Brand, d.Circle.String, d.Region.String, d.Dse, strconv.Itoa(d.GrossAdds), d.GASlab, strconv.FormatFloat(d.NetSecondaryMn, 'f', -1, 64), d.NetSecSlab, strconv.Itoa(d.DayNum)}
}

type SdpData struct {
	MonthID        string      `db:"month_id" csv:"month_id"`
	Period         string      `db:"period" csv:"period"`
	AsofDate       string      `db:"asof_date" csv:"asof_date"`
	Brand          string      `db:"brand" csv:"brand"`
	Circle         null.String `db:"circle" csv:"circle"`
	Region         null.String `db:"region" csv:"region"`
	SdpFlag        string      `db:"flag" csv:"flag"`
	Sdp            string      `db:"dist_id" csv:"dist_id"`
	GrossAdds      int         `db:"ga" csv:"ga"`
	NetSecondaryMn float64     `db:"net_secondary_mn" csv:"net_secondary_mn"`
	DayNum         int         `db:"day_no" csv:"day_no"`
}

func (d SdpData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "brand", "circle", "region", "flag", "dist_id", "ga", "net_secondary_mn", "day_no"}
}

func (d SdpData) GetRowValues() []string {
	return []string{d.MonthID, d.Period, d.AsofDate, d.Brand, d.Circle.String, d.Region.String, d.SdpFlag, d.Sdp, strconv.Itoa(d.GrossAdds), strconv.FormatFloat(d.NetSecondaryMn, 'f', -1, 64), strconv.Itoa(d.DayNum)}
}

type PartnerKpiData struct {
	PtGaSlabCount  map[string]int
	PtSecSlabCount map[string]int
	TotalGA        int
	TotalSec       float64
	PtCount        int
}

type RegionalPartnerReportData struct {
	RegionType string
	RegionName string
	MTD        *PartnerKpiData
	LMTD       *PartnerKpiData
	FM         map[string]*PartnerKpiData
}

func NewRegionalPartnerReportData(regionType, regionName string) *RegionalPartnerReportData {
	return &RegionalPartnerReportData{
		RegionType: regionType,
		RegionName: regionName,
		MTD:        &PartnerKpiData{PtGaSlabCount: make(map[string]int), PtSecSlabCount: make(map[string]int)},
		LMTD:       &PartnerKpiData{PtGaSlabCount: make(map[string]int), PtSecSlabCount: make(map[string]int)},
		FM:         make(map[string]*PartnerKpiData),
	}
}

type PartnerReportData struct {
	FmList       []string
	CircleData   map[string]*RegionalPartnerReportData
	RegionalData map[string]*RegionalPartnerReportData
	NationalData *RegionalPartnerReportData
}

func NewPartnerReportData() *PartnerReportData {
	return &PartnerReportData{
		CircleData:   make(map[string]*RegionalPartnerReportData),
		RegionalData: make(map[string]*RegionalPartnerReportData),
		NationalData: NewRegionalPartnerReportData("NATIONAL", "INDONESIA"),
	}
}

type SdpReport struct {
	AsofDate time.Time
	IM3      *PartnerReportData
	Three    *PartnerReportData
	IOH      *PartnerReportData
}

type DseReport struct {
	AsofDate time.Time
	IM3      *PartnerReportData
	Three    *PartnerReportData
	IOH      *PartnerReportData
}

type SdpDseReport struct {
	Sdp               *SdpReport
	Dse               *DseReport
	RawDataZipContent []byte
}
