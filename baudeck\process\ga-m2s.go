package process

import (
	"context"
	"encoding/csv"
	"fmt"
	"golang.org/x/sync/errgroup"
	"gopkg.in/guregu/null.v4"
	"io"
	"io/fs"
	"os"
	"slices"
	"strconv"
	"strings"
	"time"

	cfg "github.com/csee-pm/etl/baudeck/config"
	"github.com/csee-pm/etl/shared/channel"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
)

type GaM2sProcess struct {
	procFS fs.ReadFileFS
}

func NewGaM2sProcess(procFS fs.ReadFileFS) GaM2sProcess {
	return GaM2sProcess{procFS: procFS}
}

func (g GaM2sProcess) GetReportData(c context.Context, workDate time.Time) (GaM2sReport, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)
	workDir := ctx.ExtractWorkDir(c)

	var err error
	if conf.Get("etl.work_date") != nil {
		mtdDate := conf.GetString("etl.work_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return GaM2sReport{}, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	conf.Set("work_date", workDate)

	var data []GaM2sData
	if cfg.UseGaM2sFromFile != "" {
		data, err = g.getGaM2sDataFromFile(c, cfg.UseGaM2sFromFile)
	} else {
		data, err = g.getGaM2sData(c, workDate)
	}

	if err != nil {
		return GaM2sReport{}, err
	}

	csvFilePath := fmt.Sprintf("%s/ga_m2s_%s.csv", workDir, time.Now().Format("20060102150405"))
	if cfg.UseGaM2sFromFile == "" {
		if err := utils.WriteToCsv(csvFilePath, data); err != nil {
			logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
		} else {
			logger.Info("CSV file written", "path", csvFilePath)
		}
	}

	return g.postProcessData(data)
}

func (g GaM2sProcess) getGaM2sData(c context.Context, mtdDate time.Time) ([]GaM2sData, error) {
	mtdDtStr := mtdDate.Format("20060102")
	mtdDtInt, err := strconv.Atoi(mtdDtStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse mtd date. %s", err)
	}

	var im3Data []GaM2sData
	var threeData []GaM2sData

	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	eg := new(errgroup.Group)

	im3Result := channel.RunAsyncContext(cCancel, func() ([]GaM2sData, error) {
		return g.getIm3GaM2sData(cCancel, mtdDtStr)
	})

	eg.Go(func() error {
		var errg error
		for res := range im3Result {
			res.Map(func(data []GaM2sData) {
				im3Data = data
			}).MapErr(func(er error) {
				errg = fmt.Errorf("failed to get IM3 GA M2S data. %s", er)
				//logger.Error("failed to get IM3 GA M2S data", "error", er)
			})
		}
		return errg
	})

	threeResult := channel.RunAsyncContext(cCancel, func() ([]GaM2sData, error) {
		return g.get3idGaM2sData(cCancel, mtdDtInt)
	})

	eg.Go(func() error {
		var errg error
		for res := range threeResult {
			res.Map(func(data []GaM2sData) {
				threeData = data
			}).MapErr(func(er error) {
				errg = fmt.Errorf("failed to get 3ID GA M2S data. %s", er)
				//logger.Error("failed to get 3ID GA M2S data", "error", er)
			})
		}
		return errg
	})

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	kabuMap, err := etlProc.GetKabuMap(c)
	if err != nil {
		return nil, err
	}

	GaM2sData := append(im3Data, threeData...)
	for i := range GaM2sData {
		kabu := strings.TrimSpace(strings.ToUpper(GaM2sData[i].Kabupaten.String))
		if segment, ok := kabuMap[kabu]; ok {
			GaM2sData[i].KabuFlag = segment
		}
	}

	return GaM2sData, nil
}

func (g GaM2sProcess) getGaM2sDataFromFile(c context.Context, fpath string) ([]GaM2sData, error) {
	logger := ctx.ExtractLogger(c)
	logger.Info("Getting GA M2S data from file", "path", fpath)

	f, err := os.Open(fpath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read()
	var data []GaM2sData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, err
		}

		ga, err := strconv.Atoi(record[8])
		if err != nil {
			return nil, fmt.Errorf("failed to parse GA. %s", err)
		}

		m2s, err := strconv.Atoi(record[9])
		if err != nil {
			return nil, fmt.Errorf("failed to parse M2S. %s", err)
		}

		data = append(data, GaM2sData{
			MonthID:   record[0],
			Period:    record[1],
			AsofDate:  record[2],
			Circle:    null.StringFrom(record[3]),
			Region:    null.StringFrom(record[4]),
			Kabupaten: null.StringFrom(record[5]),
			KabuFlag:  record[6],
			Brand:     record[7],
			Kpi: Kpi{
				GrossAdds: ga,
				M2s:       m2s,
			},
		})
	}

	return data, nil
}

func (g GaM2sProcess) getIm3GaM2sData(c context.Context, mtdDate string) ([]GaM2sData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := g.procFS.ReadFile("files/im3_ga_m2s.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate},
	}

	logger.Info("Getting IM3 GA M2S data")

	return etlProc.QueryImpalaData[GaM2sData](c, string(buf), params)
}

func (g GaM2sProcess) get3idGaM2sData(c context.Context, mtdDate int) ([]GaM2sData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := g.procFS.ReadFile("files/3id_ga_m2s.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_int": {Name: "mtd_dt_int", Value: mtdDate},
	}

	logger.Info("Getting 3ID GA M2S data")

	return etlProc.QueryGreenplumData[GaM2sData](c, string(buf), params)
}

func (g GaM2sProcess) postProcessData(data []GaM2sData) (GaM2sReport, error) {
	brandMap := make(map[string]*GaM2sReportData)
	iohData := newGaM2sReportData()

	fmMap := make(map[string]struct{})

	for _, d := range data {
		brand := d.Brand
		if _, ok := brandMap[brand]; !ok {
			brandMap[brand] = newGaM2sReportData()
		}
		brandData := brandMap[brand]

		circle := strings.TrimSpace(d.Circle.String)
		if circle == "JAYA" {
			circle = "JAKARTA RAYA"
		}

		region := strings.TrimSpace(d.Region.String)
		flag := strings.TrimSpace(strings.ToUpper(d.KabuFlag))
		if flag == "PART OF 203" {
			flag = "LOSS KABU"
		}

		if d.Period == "MTD" {
			asofDate, err := time.Parse("20060102", d.AsofDate)
			if err != nil {
				return GaM2sReport{}, err
			}
			brandData.AsofDate = asofDate
			iohData.AsofDate = asofDate
		}

		if _, ok := brandData.RegionalMap[region]; !ok {
			brandData.RegionalMap[region] = CreateRegionalGaM2sKabuData("REGION", region)
			brandData.RegionalKabuMap[region] = make(map[string]*RegionalGaM2sKabuData)
		}
		regionData := brandData.RegionalMap[region]

		if _, ok := iohData.RegionalMap[region]; !ok {
			iohData.RegionalMap[region] = CreateRegionalGaM2sKabuData("REGION", region)
			iohData.RegionalKabuMap[region] = make(map[string]*RegionalGaM2sKabuData)
		}
		iohRegionData := iohData.RegionalMap[region]

		if _, ok := brandData.RegionalKabuMap[region][flag]; !ok {
			brandData.RegionalKabuMap[region][flag] = CreateRegionalGaM2sKabuData("FLAG", flag)
		}
		regionKabuData := brandData.RegionalKabuMap[region][flag]

		if _, ok := iohData.RegionalKabuMap[region][flag]; !ok {
			iohData.RegionalKabuMap[region][flag] = CreateRegionalGaM2sKabuData("FLAG", flag)
		}
		iohRegionKabuData := iohData.RegionalKabuMap[region][flag]

		if _, ok := brandData.CircleMap[circle]; !ok {
			brandData.CircleMap[circle] = CreateRegionalGaM2sKabuData("CIRCLE", circle)
		}
		circleData := brandData.CircleMap[circle]

		if _, ok := iohData.CircleMap[circle]; !ok {
			iohData.CircleMap[circle] = CreateRegionalGaM2sKabuData("CIRCLE", circle)
		}
		iohCircleData := iohData.CircleMap[circle]

		if _, ok := brandData.NationalKabuMap[flag]; !ok {
			brandData.NationalKabuMap[flag] = CreateRegionalGaM2sKabuData("FLAG", flag)
		}
		nationalKabuData := brandData.NationalKabuMap[flag]

		if _, ok := iohData.NationalKabuMap[flag]; !ok {
			iohData.NationalKabuMap[flag] = CreateRegionalGaM2sKabuData("FLAG", flag)
		}
		iohNationalKabuData := iohData.NationalKabuMap[flag]

		nationalData := brandData.NationalData
		iohNationalData := iohData.NationalData

		switch d.Period {
		case "MTD":
			regionData.MTD.GrossAdds += d.GrossAdds
			regionData.MTD.M2s += d.M2s
			regionData.MtdMonth = d.MonthID
			regionData.KabuCount++

			regionKabuData.MTD.GrossAdds = d.GrossAdds
			regionKabuData.MTD.M2s = d.M2s
			regionKabuData.MtdMonth = d.MonthID
			regionKabuData.KabuCount++

			iohRegionData.MTD.GrossAdds += d.GrossAdds
			iohRegionData.MTD.M2s += d.M2s
			iohRegionData.MtdMonth = d.MonthID
			iohRegionData.KabuCount++

			iohRegionKabuData.MTD.GrossAdds = d.GrossAdds
			iohRegionKabuData.MTD.M2s = d.M2s
			iohRegionKabuData.MtdMonth = d.MonthID
			iohRegionKabuData.KabuCount++

			circleData.MTD.GrossAdds += d.GrossAdds
			circleData.MTD.M2s += d.M2s
			circleData.MtdMonth = d.MonthID
			circleData.KabuCount++

			iohCircleData.MTD.GrossAdds += d.GrossAdds
			iohCircleData.MTD.M2s += d.M2s
			iohCircleData.MtdMonth = d.MonthID
			iohCircleData.KabuCount++

			nationalData.MTD.GrossAdds += d.GrossAdds
			nationalData.MTD.M2s += d.M2s
			nationalData.MtdMonth = d.MonthID
			nationalData.KabuCount++

			iohNationalData.MTD.GrossAdds += d.GrossAdds
			iohNationalData.MTD.M2s += d.M2s
			iohNationalData.MtdMonth = d.MonthID
			iohNationalData.KabuCount++

			nationalKabuData.MTD.GrossAdds += d.GrossAdds
			nationalKabuData.MTD.M2s += d.M2s
			nationalKabuData.MtdMonth = d.MonthID
			nationalKabuData.KabuCount++

			iohNationalKabuData.MTD.GrossAdds += d.GrossAdds
			iohNationalKabuData.MTD.M2s += d.M2s
			iohNationalKabuData.MtdMonth = d.MonthID
			iohNationalKabuData.KabuCount++
		case "LMTD":
			regionData.LMTD.GrossAdds += d.GrossAdds
			regionData.LMTD.M2s += d.M2s
			regionData.LmtdMonth = d.MonthID

			regionKabuData.LMTD.GrossAdds = d.GrossAdds
			regionKabuData.LMTD.M2s = d.M2s
			regionKabuData.LmtdMonth = d.MonthID

			iohRegionData.LMTD.GrossAdds += d.GrossAdds
			iohRegionData.LMTD.M2s += d.M2s
			iohRegionData.LmtdMonth = d.MonthID

			iohRegionKabuData.LMTD.GrossAdds = d.GrossAdds
			iohRegionKabuData.LMTD.M2s = d.M2s
			iohRegionKabuData.LmtdMonth = d.MonthID

			circleData.LMTD.GrossAdds += d.GrossAdds
			circleData.LMTD.M2s += d.M2s
			circleData.LmtdMonth = d.MonthID

			iohCircleData.LMTD.GrossAdds += d.GrossAdds
			iohCircleData.LMTD.M2s += d.M2s
			iohCircleData.LmtdMonth = d.MonthID

			nationalData.LMTD.GrossAdds += d.GrossAdds
			nationalData.LMTD.M2s += d.M2s
			nationalData.LmtdMonth = d.MonthID

			iohNationalData.LMTD.GrossAdds += d.GrossAdds
			iohNationalData.LMTD.M2s += d.M2s
			iohNationalData.LmtdMonth = d.MonthID

			nationalKabuData.LMTD.GrossAdds += d.GrossAdds
			nationalKabuData.LMTD.M2s += d.M2s
			nationalKabuData.LmtdMonth = d.MonthID

			iohNationalKabuData.LMTD.GrossAdds += d.GrossAdds
			iohNationalKabuData.LMTD.M2s += d.M2s
			iohNationalKabuData.LmtdMonth = d.MonthID

		case "FM":
			fmMap[d.MonthID] = struct{}{}
			if _, ok := regionData.FM[d.MonthID]; !ok {
				regionData.FM[d.MonthID] = &Kpi{}
			}
			regionData.FM[d.MonthID].GrossAdds += d.GrossAdds
			regionData.FM[d.MonthID].M2s += d.M2s

			if _, ok := regionKabuData.FM[d.MonthID]; !ok {
				regionKabuData.FM[d.MonthID] = &Kpi{}
			}
			regionKabuData.FM[d.MonthID].GrossAdds = d.GrossAdds
			regionKabuData.FM[d.MonthID].M2s = d.M2s

			if _, ok := iohRegionData.FM[d.MonthID]; !ok {
				iohRegionData.FM[d.MonthID] = &Kpi{}
			}
			iohRegionData.FM[d.MonthID].GrossAdds += d.GrossAdds
			iohRegionData.FM[d.MonthID].M2s += d.M2s

			if _, ok := iohRegionKabuData.FM[d.MonthID]; !ok {
				iohRegionKabuData.FM[d.MonthID] = &Kpi{}
			}
			iohRegionKabuData.FM[d.MonthID].GrossAdds = d.GrossAdds
			iohRegionKabuData.FM[d.MonthID].M2s = d.M2s

			if _, ok := circleData.FM[d.MonthID]; !ok {
				circleData.FM[d.MonthID] = &Kpi{}
			}
			circleData.FM[d.MonthID].GrossAdds += d.GrossAdds
			circleData.FM[d.MonthID].M2s += d.M2s

			if _, ok := iohCircleData.FM[d.MonthID]; !ok {
				iohCircleData.FM[d.MonthID] = &Kpi{}
			}
			iohCircleData.FM[d.MonthID].GrossAdds += d.GrossAdds
			iohCircleData.FM[d.MonthID].M2s += d.M2s

			if _, ok := nationalData.FM[d.MonthID]; !ok {
				nationalData.FM[d.MonthID] = &Kpi{}
			}
			nationalData.FM[d.MonthID].GrossAdds += d.GrossAdds
			nationalData.FM[d.MonthID].M2s += d.M2s

			if _, ok := iohNationalData.FM[d.MonthID]; !ok {
				iohNationalData.FM[d.MonthID] = &Kpi{}
			}
			iohNationalData.FM[d.MonthID].GrossAdds += d.GrossAdds
			iohNationalData.FM[d.MonthID].M2s += d.M2s

			if _, ok := nationalKabuData.FM[d.MonthID]; !ok {
				nationalKabuData.FM[d.MonthID] = &Kpi{}
			}
			nationalKabuData.FM[d.MonthID].GrossAdds += d.GrossAdds
			nationalKabuData.FM[d.MonthID].M2s += d.M2s

			if _, ok := iohNationalKabuData.FM[d.MonthID]; !ok {
				iohNationalKabuData.FM[d.MonthID] = &Kpi{}
			}
			iohNationalKabuData.FM[d.MonthID].GrossAdds += d.GrossAdds
			iohNationalKabuData.FM[d.MonthID].M2s += d.M2s
		}
	}

	fmList := utils.MapToList(fmMap, func(key string, value struct{}) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 4 {
		fmList = fmList[:4]
	}
	iohData.FmList = fmList
	brandMap["IM3"].FmList = fmList
	brandMap["3ID"].FmList = fmList

	return GaM2sReport{
		IOH:   iohData,
		IM3:   brandMap["IM3"],
		Three: brandMap["3ID"],
	}, nil
}

func newGaM2sReportData() *GaM2sReportData {
	return &GaM2sReportData{
		CircleMap:       make(map[string]*RegionalGaM2sKabuData),
		RegionalMap:     make(map[string]*RegionalGaM2sKabuData),
		RegionalKabuMap: make(map[string]map[string]*RegionalGaM2sKabuData),
		NationalData: &RegionalGaM2sKabuData{
			EntityType: "NATIONAL",
			EntityName: "INDONESIA",
			MTD:        &Kpi{},
			LMTD:       &Kpi{},
			FM:         make(map[string]*Kpi),
		},
		NationalKabuMap: make(map[string]*RegionalGaM2sKabuData),
	}
}

var (
	GaM2sSheet           = "to ppt"
	GaM2sMonthColOffsets = []int{0, 3, 6}
	GaM2sGaColOffset     = 1
	GaM2sM2sPctColOffset = 4
	GaM2sM2sColOffset    = 7
	GaM2sMtdRowOffset    = 4
	GaM2sLmtdRowOffset   = 3
)

func (g GaM2sProcess) WriteReport(xl *excelize.File, data GaM2sReport) error {
	iohStart := xlutil.Cell(6, 19)
	im3Start := xlutil.Cell(16, 19)
	threeStart := xlutil.Cell(26, 19)

	circleColMap := map[string]xlutil.CellStruct{
		"JAKARTA RAYA": xlutil.Cell(6, 28),
		"JAVA":         xlutil.Cell(16, 28),
		"KALISUMAPA":   xlutil.Cell(26, 28),
		"SUMATERA":     xlutil.Cell(36, 28),
	}

	kabuColMap := map[string]xlutil.CellStruct{
		"WINNING":       xlutil.Cell(6, 37),
		"MUST WIN 50":   xlutil.Cell(16, 37),
		"REST MUST WIN": xlutil.Cell(26, 37),
		"SUPER 88":      xlutil.Cell(36, 37),
	}

	// do IOH
	if err := g.writeReport(xl, data.IOH.NationalData, iohStart.Row, iohStart.Col); err != nil {
		return err
	}

	// do IM3
	if err := g.writeReport(xl, data.IM3.NationalData, im3Start.Row, im3Start.Col); err != nil {
		return err
	}

	// do 3ID
	if err := g.writeReport(xl, data.Three.NationalData, threeStart.Row, threeStart.Col); err != nil {
		return err
	}

	// do circles
	for circle, circleData := range data.IOH.CircleMap {
		if circle == "" {
			continue
		}
		if err := g.writeReport(xl, circleData, circleColMap[circle].Row, circleColMap[circle].Col); err != nil {
			return err
		}
	}

	// do kabu
	for kabu, kabuData := range data.IOH.NationalKabuMap {
		if kabu == "" {
			continue
		}
		if err := g.writeReport(xl, kabuData, kabuColMap[kabu].Row, kabuColMap[kabu].Col); err != nil {
			return err
		}
	}

	return nil
}

func (g GaM2sProcess) writeReport(xl *excelize.File, data *RegionalGaM2sKabuData, startRow int, startCol int) error {
	shName := GaM2sSheet

	if err := g.populateM2Ga(data); err != nil {
		return err
	}

	fmlist := utils.MapToList(data.FM, func(key string, value *Kpi) string {
		return key
	})

	slices.SortFunc(fmlist, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmlist) > 3 {
		fmlist = fmlist[:3]
	}

	for i, mth := range fmlist {
		if i > 2 {
			break
		}

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		mthName := mthDate.Format("Jan-06")
		r := startRow + (2 - i)
		for _, ofs := range GaM2sMonthColOffsets {
			mthCell := xlutil.Cell(r, startCol+ofs).Address()
			//fmt.Printf("write %s %s month %s in cell %s(row: %d, col: %d)\n", data.EntityName, data.EntityName, mthName, mthCell, r, startCol+ofs)
			if err := xl.SetCellValue(shName, mthCell, mthName); err != nil {
				return fmt.Errorf("failed to write month name. %s", err)
			}
		}

		m2sPct := 0.0
		if data.FM[mth].GaM2 > 0 {
			m2sPct = float64(data.FM[mth].M2s) / float64(data.FM[mth].GaM2)
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol+GaM2sM2sPctColOffset).Address(), m2sPct); err != nil {
			return fmt.Errorf("failed to write m2s pct. %s", err)
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol+GaM2sM2sColOffset).Address(), data.FM[mth].M2s); err != nil {
			return fmt.Errorf("failed to write m2s. %s", err)
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol+GaM2sGaColOffset).Address(), data.FM[mth].GrossAdds); err != nil {
			return fmt.Errorf("failed to write ga. %s", err)
		}
	}

	mtdM2sPct := 0.0
	if data.MTD.GaM2 > 0 {
		mtdM2sPct = float64(data.MTD.M2s) / float64(data.MTD.GaM2)
	}

	lmtdM2sPct := 0.0
	if data.LMTD.GaM2 > 0 {
		lmtdM2sPct = float64(data.LMTD.M2s) / float64(data.LMTD.GaM2)
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+GaM2sMtdRowOffset, startCol+GaM2sM2sPctColOffset).Address(), mtdM2sPct); err != nil {
		return fmt.Errorf("failed to write mtd m2s pct. %s", err)
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+GaM2sLmtdRowOffset, startCol+GaM2sM2sPctColOffset).Address(), lmtdM2sPct); err != nil {
		return fmt.Errorf("failed to write lmtd m2s pct. %s", err)
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+GaM2sMtdRowOffset, startCol+GaM2sM2sColOffset).Address(), data.MTD.M2s); err != nil {
		return fmt.Errorf("failed to write mtd m2s. %s", err)
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+GaM2sLmtdRowOffset, startCol+GaM2sM2sColOffset).Address(), data.LMTD.M2s); err != nil {
		return fmt.Errorf("failed to write lmtd m2s. %s", err)
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+GaM2sMtdRowOffset, startCol+GaM2sGaColOffset).Address(), data.MTD.GrossAdds); err != nil {
		return fmt.Errorf("failed to write mtd ga. %s", err)
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+GaM2sLmtdRowOffset, startCol+GaM2sGaColOffset).Address(), data.LMTD.GrossAdds); err != nil {
		return fmt.Errorf("failed to write lmtd ga. %s", err)
	}

	return nil
}

func (g GaM2sProcess) populateM2Ga(data *RegionalGaM2sKabuData) error {
	mtdMonth, err := time.Parse("200601", data.MtdMonth)
	if err != nil {
		return err
	}

	lmtdMonth, err := time.Parse("200601", data.LmtdMonth)
	if err != nil {
		return err
	}

	mtdM2Month := mtdMonth.AddDate(0, -2, 0).Format("200601")
	lmtdM2Month := lmtdMonth.AddDate(0, -2, 0).Format("200601")

	data.MTD.GaM2 = data.FM[mtdM2Month].GrossAdds
	data.LMTD.GaM2 = data.FM[lmtdM2Month].GrossAdds

	for mth := range data.FM {
		mthMonth, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		m2sMonth := mthMonth.AddDate(0, -2, 0).Format("200601")
		if d, ok := data.FM[m2sMonth]; ok {
			data.FM[mth].GaM2 = d.GrossAdds
		}
	}

	return nil
}
