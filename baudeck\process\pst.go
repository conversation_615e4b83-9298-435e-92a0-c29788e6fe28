package process

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"math"
	"os"
	"path/filepath"
	"slices"
	"sort"
	"strconv"
	"strings"
	"time"

	cfg "github.com/csee-pm/etl/baudeck/config"

	"io/fs"

	"github.com/csee-pm/etl/shared/channel"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
	"golang.org/x/sync/errgroup"
	"gopkg.in/guregu/null.v4"
)

type PstProcess struct {
	procFS fs.ReadFileFS
}

func NewPstProcess(procFS fs.ReadFileFS) PstProcess {
	return PstProcess{procFS}
}

func (pst PstProcess) GetReportData(c context.Context, workDate time.Time) (PSTReport, error) {
	conf := ctx.ExtractConfig(c)

	var err error
	if conf.Get("etl.work_date") != nil {
		mtdDate := conf.GetString("etl.work_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return PSTReport{}, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	logger := ctx.ExtractLogger(c)
	logger.Debug("work months", "mtd", workDate.Format("20060102"))

	workDir := ctx.ExtractWorkDir(c)

	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var mtdData []MtdPSTData
	var fmData []FmPSTData

	// get MTD data
	mtdResult := channel.RunAsyncContext(cCancel, func() ([]MtdPSTData, error) {
		if cfg.UsePstMtdFromFile != "" {
			return pst.getMtdDataFromFile(cCancel, cfg.UsePstMtdFromFile)
		}
		return pst.getMtdData(cCancel, workDate)
	})

	g := new(errgroup.Group)

	err = nil
	g.Go(func() error {
		var errg error
		for res := range mtdResult {
			res.Map(func(data []MtdPSTData) {
				mtdData = data
			}).MapErr(func(er error) {
				errg = fmt.Errorf("failed to get MTD data for PST. %s", er)
				logger.Error("failed to get MTD data", "error", er)
			})
		}
		return errg
	})

	fmResult := channel.RunAsyncContext(cCancel, func() ([]FmPSTData, error) {
		return pst.getFmData(cCancel, workDate)
	})

	g.Go(func() error {
		var errg error
		for res := range fmResult {
			res.Map(func(data []FmPSTData) {
				fmData = data
			}).MapErr(func(er error) {
				errg = fmt.Errorf("failed to get FM data for PST. %s", er)
				logger.Error("failed to get FM data", "error", er)
			})
		}
		return errg
	})

	if err := g.Wait(); err != nil {
		return PSTReport{}, err
	}

	csvFilePath := fmt.Sprintf("%s/pst_mtd_%s.csv", workDir, time.Now().Format("20060102150405"))

	if cfg.UsePstMtdFromFile == "" {
		if err := utils.WriteToCsv(csvFilePath, mtdData); err != nil {
			logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
		} else {
			logger.Info("CSV file written", "path", csvFilePath)
		}
	}

	if cfg.UsePstFmFromFile == "" {
		csvFilePath := fmt.Sprintf("%s/pst_fm_%s.csv", workDir, time.Now().Format("20060102150405"))
		if err := utils.WriteToCsv(csvFilePath, fmData); err != nil {
			logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
		} else {
			logger.Info("CSV file written", "path", csvFilePath)
		}
	}

	reportData, err := pst.postProcessData(mtdData, fmData)
	if err != nil {
		return PSTReport{}, err
	}

	return PSTReport{
		MtdDate: workDate,
		IM3:     CreatePSTReportData(reportData.MTD.NationalMtdIm3, reportData.Fm.NationalFmIm3, reportData.MTD.CircleMtdIm3, reportData.Fm.CircleFmIm3, reportData.MTD.RegionalMtdIm3, reportData.Fm.RegionalFmIm3),
		Three:   CreatePSTReportData(reportData.MTD.NationalMtd3id, reportData.Fm.NationalFm3id, reportData.MTD.CircleMtd3id, reportData.Fm.CircleFm3id, reportData.MTD.RegionalMtd3id, reportData.Fm.RegionalFm3id),
		IOH:     CreatePSTReportData(reportData.MTD.NationalMtdIOH, reportData.Fm.NationalFmIOH, reportData.MTD.CircleMtdIOH, reportData.Fm.CircleFmIOH, reportData.MTD.RegionalMtdIOH, reportData.Fm.RegionalFmIOH),
	}, nil
}

func (pst PstProcess) getMtdData(c context.Context, mtdDate time.Time) ([]MtdPSTData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := pst.procFS.ReadFile("files/cwn_pst_mtd.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format(utils.ToGoDateFormat("YYYYMMDD"))},
	}

	logger.Info("Getting PST MTD data")
	return etlProc.QueryImpalaData[MtdPSTData](c, string(buf), params)
}

func (pst PstProcess) getMtdDataFromFile(c context.Context, filePath string) ([]MtdPSTData, error) {
	logger := ctx.ExtractLogger(c)

	f, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	logger.Info("Getting MTD data from CSV file", "path", filePath)

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read()

	var data []MtdPSTData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, err
		}

		primaryVal := null.NewFloat(0, false)
		primary, err := strconv.ParseFloat(record[6], 64)
		if err == nil {
			primaryVal = null.FloatFrom(primary)
		}

		secondaryVal := null.NewFloat(0, false)
		secondary, err := strconv.ParseFloat(record[7], 64)
		if err == nil {
			secondaryVal = null.FloatFrom(secondary)
		}

		tertiaryVal := null.NewFloat(0, false)
		tertiary, err := strconv.ParseFloat(record[8], 64)
		if err == nil {
			tertiaryVal = null.FloatFrom(tertiary)
		}

		data = append(data, MtdPSTData{
			MonthID:  record[0],
			Period:   record[1],
			AsofDate: record[2],
			RegionPSTKpi: RegionPSTKpi{
				Circle: null.StringFrom(record[3]),
				Region: null.StringFrom(record[4]),
				Brand:  record[5],
				PSTKpi: PSTKpi{
					Primary:   primaryVal,
					Secondary: secondaryVal,
					Tertiary:  tertiaryVal,
				},
			},
		})
	}

	return data, nil
}

func (pst PstProcess) getFmData(c context.Context, mtdDate time.Time) ([]FmPSTData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := pst.procFS.ReadFile("files/cwn_pst_fm.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format(utils.ToGoDateFormat("YYYYMMDD"))},
	}

	logger.Info("Getting PST FullMonth data")
	return etlProc.QueryImpalaData[FmPSTData](c, string(buf), params)
}

func (pst PstProcess) getFmDataFromFile(c context.Context) ([]FmPSTData, error) {
	logger := ctx.ExtractLogger(c)
	rootdir := ctx.ExtractRootDir(c)
	fmCsv := filepath.Join(rootdir, "PST_FM.csv")

	var data []FmPSTData

	f, err := os.Open(fmCsv)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	logger.Info("Getting PST FullMonth data from CSV file", "path", fmCsv)

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read()
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, err
		}

		primary, err := strconv.ParseFloat(record[4], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse primary amount. %s", err)
		}

		secondary, err := strconv.ParseFloat(record[5], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse secondary amount. %s", err)
		}

		tertiary, err := strconv.ParseFloat(record[6], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse tertiary amount. %s", err)
		}

		data = append(data, FmPSTData{
			MonthID: record[0],
			RegionPSTKpi: RegionPSTKpi{
				Circle: null.StringFrom(record[1]),
				Region: null.StringFrom(record[2]),
				Brand:  record[3],
				PSTKpi: PSTKpi{
					Primary:   null.FloatFrom(primary),
					Secondary: null.FloatFrom(secondary),
					Tertiary:  null.FloatFrom(tertiary),
				},
			},
		})
	}

	return data, nil
}

func (pst PstProcess) postProcessData(mtd []MtdPSTData, fm []FmPSTData) (ProcessedPSTReportData, error) {
	mtdData, err := pst.postProcessMtdData(mtd)
	if err != nil {
		return ProcessedPSTReportData{}, err
	}

	fmData, err := pst.postProcessFmData(fm)
	if err != nil {
		return ProcessedPSTReportData{}, err
	}

	fmMonthMap := make(map[string]struct{})
	mapProcessor := func(key string, val map[string]*FmPSTData) {
		for mkey := range val {
			fmMonthMap[mkey] = struct{}{}
		}
	}

	utils.MapProcessor(fmData.CircleFm3id, mapProcessor)
	utils.MapProcessor(fmData.RegionalFm3id, mapProcessor)
	utils.MapProcessor(fmData.CircleFmIm3, mapProcessor)
	utils.MapProcessor(fmData.RegionalFmIm3, mapProcessor)

	fmList := utils.MapToList(fmMonthMap, func(key string, value struct{}) string {
		return key
	})

	sort.Strings(fmList)

	return ProcessedPSTReportData{
		FmList: fmList,
		MTD:    mtdData,
		Fm:     fmData,
	}, nil
}

func (pst PstProcess) postProcessMtdData(data []MtdPSTData) (MtdPSTReportData, error) {
	regMapIm3 := make(map[string]*MtdPSTKpi)
	regMap3id := make(map[string]*MtdPSTKpi)
	regMapIOH := make(map[string]*MtdPSTKpi)
	cirMapIm3 := make(map[string]*MtdPSTKpi)
	cirMap3id := make(map[string]*MtdPSTKpi)
	cirMapIOH := make(map[string]*MtdPSTKpi)

	natIm3 := NewMTDPSTKpi("IM3", null.NewString("", false), null.NewString("", false))
	nat3id := NewMTDPSTKpi("3ID", null.NewString("", false), null.NewString("", false))
	natIOH := NewMTDPSTKpi("IOH", null.NewString("", false), null.NewString("", false))

	for i := range data {
		mtd := data[i]
		reg := mtd.Region.String
		cir := mtd.Circle.String
		brand := mtd.Brand

		var regMap = regMapIm3
		var cirMap = cirMapIm3
		var natBrand = natIm3

		if brand == "3ID" {
			regMap = regMap3id
			cirMap = cirMap3id
			natBrand = nat3id
		}

		if _, ok := regMap[reg]; !ok {
			regMap[reg] = &MtdPSTKpi{}
		}

		if _, ok := regMapIOH[reg]; !ok {
			regMapIOH[reg] = NewMTDPSTKpi("IOH", mtd.Circle, mtd.Region)
		}

		if _, ok := cirMap[cir]; !ok {
			cirMap[cir] = NewMTDPSTKpi(mtd.Brand, mtd.Circle, null.NewString("", false))
		}

		if _, ok := cirMapIOH[cir]; !ok {
			cirMapIOH[cir] = NewMTDPSTKpi("IOH", mtd.Circle, null.NewString("", false))
		}

		if strings.ToUpper(mtd.Period) == "MTD" {
			regMap[reg].MTD = MtdPSTData{
				MonthID:  mtd.MonthID,
				Period:   mtd.Period,
				AsofDate: mtd.AsofDate,
				RegionPSTKpi: RegionPSTKpi{
					Circle: mtd.Circle,
					Region: mtd.Region,
					Brand:  mtd.Brand,
					PSTKpi: PSTKpi{
						Primary:   mtd.Primary,
						Secondary: mtd.Secondary,
						Tertiary:  mtd.Tertiary,
					},
				},
			}

			regMapIOH[reg].MTD.MonthID = mtd.MonthID
			regMapIOH[reg].MTD.Period = mtd.Period
			regMapIOH[reg].MTD.AsofDate = mtd.AsofDate
			regMapIOH[reg].MTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			regMapIOH[reg].MTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			regMapIOH[reg].MTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64

			natBrand.MTD.MonthID = mtd.MonthID
			natBrand.MTD.Period = mtd.Period
			natBrand.MTD.AsofDate = mtd.AsofDate
			natBrand.MTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			natBrand.MTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			natBrand.MTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64

			natIOH.MTD.MonthID = mtd.MonthID
			natIOH.MTD.Period = mtd.Period
			natIOH.MTD.AsofDate = mtd.AsofDate
			natIOH.MTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			natIOH.MTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			natIOH.MTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64

			cirMap[cir].MTD.MonthID = mtd.MonthID
			cirMap[cir].MTD.Period = mtd.Period
			cirMap[cir].MTD.AsofDate = mtd.AsofDate
			cirMap[cir].MTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			cirMap[cir].MTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			cirMap[cir].MTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64

			cirMapIOH[cir].MTD.MonthID = mtd.MonthID
			cirMapIOH[cir].MTD.Period = mtd.Period
			cirMapIOH[cir].MTD.AsofDate = mtd.AsofDate
			cirMapIOH[cir].MTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			cirMapIOH[cir].MTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			cirMapIOH[cir].MTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64
		}

		if strings.ToUpper(mtd.Period) == "LMTD" {
			regMap[reg].LMTD = MtdPSTData{
				MonthID:  mtd.MonthID,
				Period:   mtd.Period,
				AsofDate: mtd.AsofDate,
				RegionPSTKpi: RegionPSTKpi{
					Circle: mtd.Circle,
					Region: mtd.Region,
					Brand:  mtd.Brand,
					PSTKpi: PSTKpi{
						Primary:   mtd.Primary,
						Secondary: mtd.Secondary,
						Tertiary:  mtd.Tertiary,
					},
				},
			}

			regMapIOH[reg].LMTD.MonthID = mtd.MonthID
			regMapIOH[reg].LMTD.Period = mtd.Period
			regMapIOH[reg].LMTD.AsofDate = mtd.AsofDate
			regMapIOH[reg].LMTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			regMapIOH[reg].LMTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			regMapIOH[reg].LMTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64

			cirMap[cir].LMTD.MonthID = mtd.MonthID
			cirMap[cir].LMTD.Period = mtd.Period
			cirMap[cir].LMTD.AsofDate = mtd.AsofDate
			cirMap[cir].LMTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			cirMap[cir].LMTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			cirMap[cir].LMTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64

			cirMapIOH[cir].LMTD.MonthID = mtd.MonthID
			cirMapIOH[cir].LMTD.Period = mtd.Period
			cirMapIOH[cir].LMTD.AsofDate = mtd.AsofDate
			cirMapIOH[cir].LMTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			cirMapIOH[cir].LMTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			cirMapIOH[cir].LMTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64

			natBrand.LMTD.MonthID = mtd.MonthID
			natBrand.LMTD.Period = mtd.Period
			natBrand.LMTD.AsofDate = mtd.AsofDate
			natBrand.LMTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			natBrand.LMTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			natBrand.LMTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64

			natIOH.LMTD.MonthID = mtd.MonthID
			natIOH.LMTD.Period = mtd.Period
			natIOH.LMTD.AsofDate = mtd.AsofDate
			natIOH.LMTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			natIOH.LMTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			natIOH.LMTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64
		}
	}

	return MtdPSTReportData{
		CircleMtdIm3:   cirMapIm3,
		RegionalMtdIm3: regMapIm3,
		CircleMtd3id:   cirMap3id,
		RegionalMtd3id: regMap3id,
		CircleMtdIOH:   cirMapIOH,
		RegionalMtdIOH: regMapIOH,
		NationalMtdIm3: natIm3,
		NationalMtd3id: nat3id,
		NationalMtdIOH: natIOH,
	}, nil
}

func (pst PstProcess) postProcessFmData(data []FmPSTData) (FmPSTReportData, error) {
	regMapIm3 := make(map[string]map[string]*FmPSTData)
	regMap3id := make(map[string]map[string]*FmPSTData)
	regMapIOH := make(map[string]map[string]*FmPSTData)
	cirMapIm3 := make(map[string]map[string]*FmPSTData)
	cirMap3id := make(map[string]map[string]*FmPSTData)
	cirMapIOH := make(map[string]map[string]*FmPSTData)

	natIm3 := make(map[string]*FmPSTData)
	nat3id := make(map[string]*FmPSTData)
	natIOH := make(map[string]*FmPSTData)

	for i := range data {
		reg := data[i].Region.String
		cir := data[i].Circle.String
		fmData := data[i]
		monthID := fmData.MonthID
		brand := fmData.Brand

		regMap := regMapIm3
		cirMap := cirMapIm3
		natBrand := natIm3

		if brand == "3ID" {
			regMap = regMap3id
			cirMap = cirMap3id
			natBrand = nat3id
		}

		if _, ok := regMap[reg]; !ok {
			regMap[reg] = make(map[string]*FmPSTData)
		}

		if _, ok := regMapIOH[reg]; !ok {
			regMapIOH[reg] = make(map[string]*FmPSTData)
		}

		if _, ok := cirMap[cir]; !ok {
			cirMap[cir] = make(map[string]*FmPSTData)
		}

		if _, ok := cirMapIOH[cir]; !ok {
			cirMapIOH[cir] = make(map[string]*FmPSTData)
		}

		regMap[reg][monthID] = &fmData

		if _, ok := regMapIOH[reg][monthID]; !ok {
			regMapIOH[reg][monthID] = &FmPSTData{
				MonthID:      monthID,
				RegionPSTKpi: NewRegionPSTKpi("IOH", fmData.Circle, fmData.Region),
			}
		}

		regMapIOH[reg][monthID].Primary.Float64 += fmData.Primary.Float64
		regMapIOH[reg][monthID].Secondary.Float64 += fmData.Secondary.Float64
		regMapIOH[reg][monthID].Tertiary.Float64 += fmData.Tertiary.Float64

		if _, ok := cirMap[cir][monthID]; !ok {
			cirMap[cir][monthID] = &FmPSTData{
				MonthID:      monthID,
				RegionPSTKpi: NewRegionPSTKpi(brand, fmData.Circle, null.NewString("", false)),
			}
		}

		cirMap[cir][monthID].Primary.Float64 += fmData.Primary.Float64
		cirMap[cir][monthID].Secondary.Float64 += fmData.Secondary.Float64
		cirMap[cir][monthID].Tertiary.Float64 += fmData.Tertiary.Float64

		if _, ok := cirMapIOH[cir][monthID]; !ok {
			cirMapIOH[cir][monthID] = &FmPSTData{
				MonthID:      monthID,
				RegionPSTKpi: NewRegionPSTKpi("IOH", fmData.Circle, null.NewString("", false)),
			}
		}

		cirMapIOH[cir][monthID].Primary.Float64 += fmData.Primary.Float64
		cirMapIOH[cir][monthID].Secondary.Float64 += fmData.Secondary.Float64
		cirMapIOH[cir][monthID].Tertiary.Float64 += fmData.Tertiary.Float64

		if _, ok := natBrand[monthID]; !ok {
			natBrand[monthID] = &FmPSTData{
				MonthID:      monthID,
				RegionPSTKpi: NewRegionPSTKpi(fmData.Brand, null.NewString("", false), null.NewString("", false)),
			}
		}

		natBrand[monthID].Primary.Float64 += fmData.Primary.Float64
		natBrand[monthID].Secondary.Float64 += fmData.Secondary.Float64
		natBrand[monthID].Tertiary.Float64 += fmData.Tertiary.Float64

		if _, ok := natIOH[monthID]; !ok {
			natIOH[monthID] = &FmPSTData{
				MonthID:      monthID,
				RegionPSTKpi: NewRegionPSTKpi("IOH", null.NewString("", false), null.NewString("", false)),
			}
		}

		natIOH[monthID].Primary.Float64 += fmData.Primary.Float64
		natIOH[monthID].Secondary.Float64 += fmData.Secondary.Float64
		natIOH[monthID].Tertiary.Float64 += fmData.Tertiary.Float64
	}

	return FmPSTReportData{
		CircleFmIm3:   cirMapIm3,
		RegionalFmIm3: regMapIm3,
		CircleFm3id:   cirMap3id,
		RegionalFm3id: regMap3id,
		CircleFmIOH:   cirMapIOH,
		RegionalFmIOH: regMapIOH,
		NationalFmIm3: natIm3,
		NationalFm3id: nat3id,
		NationalFmIOH: natIOH,
	}, nil
}

var (
	PSTBrandPrimaryCol  = 3
	PSTCirclePrimaryCol = 8
)

func (pst PstProcess) getPSTReportSheet() string {
	return "to ppt"
}

func (pst PstProcess) WriteReport(xl *excelize.File, data *PSTReport) error {
	natIm3 := xlutil.Cell(16, PSTBrandPrimaryCol)
	nat3ID := xlutil.Cell(26, PSTBrandPrimaryCol)
	natIOH := xlutil.Cell(6, PSTBrandPrimaryCol)

	dIM3 := data.IM3
	d3ID := data.Three
	dIOH := data.IOH

	cirJaya := xlutil.Cell(6, PSTCirclePrimaryCol)
	cirJava := xlutil.Cell(16, PSTCirclePrimaryCol)
	cirKalisumapa := xlutil.Cell(26, PSTCirclePrimaryCol)
	cirSum := xlutil.Cell(36, PSTCirclePrimaryCol)

	// do IM3
	if err := pst.writeReport(xl, dIM3.NationalData, natIm3.Row, natIm3.Col); err != nil {
		return err
	}

	// do 3ID
	if err := pst.writeReport(xl, d3ID.NationalData, nat3ID.Row, nat3ID.Col); err != nil {
		return err
	}

	// do IOH
	if err := pst.writeReport(xl, dIOH.NationalData, natIOH.Row, natIOH.Col); err != nil {
		return err
	}

	// do JAYA
	if err := pst.writeReport(xl, dIM3.CircleData["JAKARTA RAYA"], cirJaya.Row, cirJaya.Col); err != nil {
		return fmt.Errorf("failed writing PST for circle JAKARTA RAYA: %w", err)
	}

	// do JAVA
	if err := pst.writeReport(xl, d3ID.CircleData["JAVA"], cirJava.Row, cirJava.Col); err != nil {
		return fmt.Errorf("failed writing PST for circle JAVA: %w", err)
	}

	// do KALISUMAPA
	if err := pst.writeReport(xl, dIOH.CircleData["KALISUMAPA"], cirKalisumapa.Row, cirKalisumapa.Col); err != nil {
		return fmt.Errorf("failed writing PST for circle KALISUMAPA: %w", err)
	}

	// do SUMATERA
	if err := pst.writeReport(xl, dIM3.CircleData["SUMATERA"], cirSum.Row, cirSum.Col); err != nil {
		return fmt.Errorf("failed writing PST for circle SUMATERA: %w", err)
	}

	return nil
}

func (pst PstProcess) writeReport(xl *excelize.File, data *PSTData, startRow, startCol int) error {
	shName := pst.getPSTReportSheet()
	if data == nil {
		return fmt.Errorf("PSTData is nil")
	}

	fmList := utils.MapToList(data.FM, func(key string, value *PSTKpi) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 3 {
		fmList = fmList[:3]
	}

	for i, mth := range fmList {
		if i > 2 {
			break
		}

		r := startRow + (2 - i)

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		mthName := mthDate.Format("Jan-06")
		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol-1).Address(), mthName); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol).Address(), data.FM[mth].Primary.Float64/math.Pow10(9)); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol+1).Address(), data.FM[mth].Secondary.Float64/math.Pow10(9)); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, startCol+2).Address(), data.FM[mth].Tertiary.Float64/math.Pow10(9)); err != nil {
			return err
		}
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+3, startCol).Address(), data.LMTD.Primary.Float64/math.Pow10(9)); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+3, startCol+1).Address(), data.LMTD.Secondary.Float64/math.Pow10(9)); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+3, startCol+2).Address(), data.LMTD.Tertiary.Float64/math.Pow10(9)); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+4, startCol).Address(), data.MTD.Primary.Float64/math.Pow10(9)); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+4, startCol+1).Address(), data.MTD.Secondary.Float64/math.Pow10(9)); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+4, startCol+2).Address(), data.MTD.Tertiary.Float64/math.Pow10(9)); err != nil {
		return err
	}

	return nil
}
