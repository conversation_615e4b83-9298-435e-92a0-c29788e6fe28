package process

import (
	"bytes"
	"context"
	"embed"
	"fmt"
	ctx "github.com/csee-pm/etl/shared/context"
	prc "github.com/csee-pm/etl/shared/process"
	"github.com/spf13/viper"
	"github.com/xuri/excelize/v2"
	"golang.org/x/sync/errgroup"
	"time"
)

//go:embed all:files
var procFS embed.FS

func cloneViper(v *viper.Viper) *viper.Viper {
	newV := viper.New()
	err := newV.MergeConfigMap(v.AllSettings())
	if err != nil {
		panic(err)
	}
	return newV
}

func RunETL(c context.Context, options ...prc.ProcessOption) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	opt := prc.NewDefaultProcessOption()
	for _, o := range options {
		o(opt)
	}

	if conf.Get("etl.work_date") != nil {
		mtdDate := conf.GetString("etl.work_date")
		workDate, err := time.Parse("20060102", mtdDate)
		if err == nil {
			opt.WorkDate = workDate
		}
	}

	pstProc := NewCwnProcess(procFS)
	pstKabuProc := NewPstKabuProcess(procFS)
	gaM2sProc := NewGaM2sProcess(procFS)
	sdpProc := NewSdpProcess(procFS)

	var pstReport CwnReport
	var pstKabuReport PstKabuReport
	var gaM2sReport GaM2sReport
	var sdpReport SdpDseReport

	g, c := errgroup.WithContext(c)

	g.Go(func() error {
		var errg error
		pstReport, errg = pstProc.GetReportData(c, opt.WorkDate)
		return errg
	})

	g.Go(func() error {
		var errg error
		pstKabuReport, errg = pstKabuProc.GetReportData(c, opt.WorkDate)
		return errg
	})

	g.Go(func() error {
		var errg error
		gaM2sReport, errg = gaM2sProc.GetReportData(c, opt.WorkDate)
		return errg
	})

	g.Go(func() error {
		var errg error
		sdpReport, errg = sdpProc.GetReportData(c, opt.WorkDate)
		return errg
	})

	if err := g.Wait(); err != nil {
		return err
	}

	//buf, err := json.MarshalIndent(pstKabuReport, "", "  ")
	//if err != nil {
	//	return err
	//}
	//fmt.Println("pstKabuReport:\n", string(buf))

	baudeckReportFile := fmt.Sprintf("%s/baudeck_update_%s.xlsx", conf.GetString("work_dir"), time.Now().Format("200601021504"))
	fpst, err := procFS.Open("files/baudeck_update.xlsx")
	if err != nil {
		return err
	}
	defer fpst.Close()

	xlPst, err := excelize.OpenReader(fpst)
	if err != nil {
		return fmt.Errorf("failed to open excel file. %s", err)
	}
	defer xlPst.Close()

	if err := pstProc.WriteReport(xlPst, pstReport); err != nil {
		return fmt.Errorf("failed to write PST report. %s", err)
	}

	if err := pstKabuProc.WriteReport(xlPst, pstKabuReport); err != nil {
		return fmt.Errorf("failed to write PST Kabu report. %s", err)
	}

	if err := gaM2sProc.WriteReport(xlPst, gaM2sReport); err != nil {
		return fmt.Errorf("failed to write GaM2s report. %s", err)
	}

	if err := sdpProc.WriteDseReport(xlPst, sdpReport.Dse); err != nil {
		return fmt.Errorf("failed to write DSE report. %s", err)
	}

	if err := sdpProc.WriteSdpReport(xlPst, sdpReport.Sdp); err != nil {
		return fmt.Errorf("failed to write SDP report. %s", err)
	}

	if err := xlPst.UpdateLinkedValue(); err != nil {
		return fmt.Errorf("failed to update PST linked values. %s", err)
	}

	if err := xlPst.SaveAs(baudeckReportFile); err != nil {
		return fmt.Errorf("failed to save PST report. %s", err)
	}

	buf, err := xlPst.WriteToBuffer()
	if err != nil {
		return fmt.Errorf("failed to write PST report to buffer. %s", err)
	}

	recipients, err := GetReportRecipients(c, "baudeck")
	if err != nil {
		return err
	}

	if len(opt.EmailRecipients) > 0 {
		recipients = opt.EmailRecipients
	}

	if !conf.GetBool("use_no_mailer") && opt.SendEmail {
		if err := SendReportEmailWithRecipients(c, "baudeck", recipients, opt.WorkDate, Attachment{FileName: baudeckReportFile, Content: bytes.NewReader(buf.Bytes())}); err != nil {
			logger.Error("failed to send Baudeck report email", "error", err)
		}
	}

	return nil
}
