with ga as (
	select
	    a.dt_id,
	    organization_id,
	    sum(net_rev) net_acq_rev,
	    count(distinct a.msisdn) ga
	from
	(
	    select
	        msisdn,
	        actvn_dt,
	        total_rev/1.11 net_rev,
	        site_id,
	        churn_back,
	        recycled,
	        dt_id
		from
		    biadm.umr_rgs_ga_90d_dly_govt
        where
            dt_id >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
        and dt_id <= ${mtd_dt_id}
        and (churn_back = 'NO' OR recycled = 'YES')
    ) a
    join
    (
        select
            msisdn,
            organization_id,
            channel_grp,
            channel_alloc,
            cluster_alloc,
            flag_sp,
    	    case
    	        when flag_sp = 'SP OLA' then 'RGUGA-Digital-OLA'
		        when channel_grp = 'TRADITIONAL' then 'RGUGA-Trad-Outlet'
                when channel_grp = 'DSF' then 'RGUGA-Trad-DSF'
                when channel_grp = 'MODERN' and channel like '%ONLINE%' then 'RGUGA-Digital-Online'
                when channel_grp = 'MODERN' then 'RGUGA-Digital-Modern'
		        else
		        case
		            when channel_alloc = 'TRADITIONAL' then 'RGUGA-Trad-Outlet'
		            when channel_alloc = 'DSF' then 'RGUGA-Trad-DSF'
		            else 'RGUGA-Others'
		        end
		    end channel_grp_snd,
    	    ga_dt
	    from
	        biadm.umr_rgs_ga_channel_mth_govt
    	where
    	    ga_dt >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
        and ga_dt <= ${mtd_dt_id}
    ) b
    on
        a.msisdn = b.msisdn
    and a.dt_id = b.ga_dt
    where
        channel_grp_snd like 'RGUGA-Trad%'
    group by 1,2
),
sec as (
    select
        dt_id,
        credit_party_id,
        sum(amount)/1.11 net_amount
    from
        biadm.omn_secondary
    where
        dt_id >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
    and dt_id <= ${mtd_dt_id}
    group by 1,2
),
kpi as (
    select
        substr(coalesce(ga.dt_id, sec.dt_id), 1, 6) month_id,
        coalesce(ga.dt_id, sec.dt_id) dt_id,
        coalesce(ga.organization_id, sec.credit_party_id) organization_id,
        coalesce(ga.ga, 0) ga,
        coalesce(sec.net_amount, 0) net_sec_amt
    from
        ga
        full outer join
        sec
        on
            ga.dt_id = sec.dt_id
        and ga.organization_id = sec.credit_party_id
),
nbs as (
    select
        *
    from
    (
        select
            site_id,
            organization_id,
            pt_nm,
        	mpc_nm,
        	parent_org_type,
            region,
            circle,
            substr(dt_id, 1, 6) month_id,
            ROW_NUMBER() OVER (partition by organization_id, substr(dt_id, 1, 6) ORDER BY dt_id desc) AS rn
        from
            biadm.omn_outlet_loc_ns
        where
            dt_id >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
        and dt_id <= ${mtd_dt_id}
    ) a
    where rn=1
),
circle_ref as
(
    select
        site_id,
        sales_area,
        mpc_nm,
        pt_nm,
        region_circle,
        circle
    from
        biadm.ref_site
)
select
    coalesce(kpi.month_id, nbs.month_id) month_id,
    case
        when coalesce(kpi.month_id, nbs.month_id) = substr(${mtd_dt_id}, 1, 6) then 'MTD'
        else 'LMTD'
    end period,
    'IM3' as brand,
    coalesce(nbs.circle,circle_ref.circle) as circle,
    coalesce(nbs.region, circle_ref.region_circle) as region,
    CASE
        WHEN parent_org_type= 'SDP' OR coalesce(nbs.mpc_nm, circle_ref.mpc_nm) LIKE 'SDP%'
        THEN 'SDP'
        ELSE 'MPC'
    END AS flag,
    coalesce(nbs.pt_nm,circle_ref.pt_nm) dist_id,
    max(kpi.dt_id) asof_date,
    sum(coalesce(kpi.ga, 0)) ga,
    round(sum(coalesce(kpi.net_sec_amt, 0)) / 1000000, 3) net_secondary_mn,
    cast(substr(${mtd_dt_id}, 7, 2) as int) day_no
from
    nbs
    left join
    kpi
    on
        nbs.organization_id = kpi.organization_id
    and nbs.month_id=kpi.month_id
    left join
    circle_ref
    on
        nbs.site_id = circle_ref.site_id
where
    kpi.dt_id >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -1 month), 'month'), 'yyyyMMdd')
and cast(substr(kpi.dt_id, 7, 2) as int) <= cast(substr(${mtd_dt_id}, 7, 2) as int)
group by 1,2,4,5,6,7

UNION ALL

select
    coalesce(kpi.month_id, nbs.month_id) month_id,
    'FM' period,
    'IM3' as brand,
    coalesce(nbs.circle, circle_ref.circle) as circle,
    coalesce(nbs.region, circle_ref.region_circle) as region,
    CASE
        WHEN parent_org_type= 'SDP' OR coalesce(nbs.mpc_nm, circle_ref.mpc_nm) LIKE 'SDP%'
        THEN 'SDP'
        ELSE 'MPC'
    END AS flag,
    coalesce(nbs.pt_nm, circle_ref.pt_nm) dist_id,
    max(kpi.dt_id) asof_date,
    sum(coalesce(kpi.ga,0)) ga,
    round(sum(coalesce(kpi.net_sec_amt,0))/1000000, 3) net_secondary_mn,
    cast(from_timestamp(date_add(date_add(to_timestamp(concat(coalesce(kpi.month_id, nbs.month_id), '01'), 'yyyyMMdd'), interval 1 month), interval -1 day), 'dd') as int) day_no
from
    nbs
    left join
    kpi
    on
        nbs.organization_id = kpi.organization_id
    and nbs.month_id=kpi.month_id
    left join
    circle_ref
    on
        nbs.site_id = circle_ref.site_id
where
    kpi.dt_id < from_timestamp(trunc(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), 'month'), 'yyyyMMdd')
group by
    1,4,5,6,7,11