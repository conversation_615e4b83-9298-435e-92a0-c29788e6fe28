with outlet as
(
    select distinct
        upper(circle) circle_,
		case
		    when region = 'HOR_MALUKU PAPUA' then 'MAPA'
    		else replace(replace(region,'HOR ',''),'ERN','')
		end region,
        retailer_qrcode,
        se_partnerid dse,
        mp3_partnerid sdp,
    	case when upper(se_category) like '%KIOSK%' then '3KIOSK' else upper(se_category) end se_category,
    	mth_id::int month_id
    from
        marketing.snd_outlet_mapping
    where
        mth_id >= to_char(to_date(${mtd_dt_int}::varchar, 'YYYYMMDD') + interval '-3 month', 'YYYYMM')
    and mth_id <= (${mtd_dt_int}/100)::varchar
),
base as (
	select
	    coalesce(ga.partner_qr_cd, sec.partner_qr_cd) partner_qr_code,
        coalesce(ga.ga,0) ga,
        coalesce (sec.secondary,0)/1.11 net_secondary,
        coalesce(ga.load_dt_sk_id, sec.dt_sk_id)dt_id
	from
	(
		select
            partner_qr_cd,
            load_dt_sk_id,
            count(distinct sbscrptn_ek_id) ga
        from
            marketing.and_snd_rgu_ga_new_detail
        where
            load_dt_sk_id >= cast(to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD') + interval '-4 month'), 'YYYYMMDD') as integer)
        and load_dt_sk_id <= ${mtd_dt_int}
        group by 1,2
	) ga
	full outer join
	(
		select
            partner_qr_cd,
            sum(value) as secondary,
            dt_sk_id
		from
		    mis.project_ioh_secondary_outlet
		where
		    dt_sk_id >= cast(to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD') + interval '-4 month'), 'YYYYMMDD') as integer)
		and dt_sk_id <= ${mtd_dt_int}
        and secondary_category = 'SALDO'
        and hierarchy_type = 'ANGIE'
        and secondary_type in ('PRT_CUANWEB','Purchase from CAN','Purchase from MP3')
		group by partner_qr_cd, dt_sk_id
	)sec
	on ga.partner_qr_cd = sec.partner_qr_cd
	and ga.load_dt_sk_id = sec.dt_sk_id
)
select
    outlet.month_id,
    case
        when coalesce(base.dt_id/100, outlet.month_id) = ${mtd_dt_int}/100 then 'MTD'
        else 'LMTD'
    end period,
    '3ID' brand,
    circle_ circle,
    region,
    se_category flag,
    sdp dist_id,
    max(base.dt_id) asof_date,
	sum(coalesce(ga,0)) ga,
	round((sum(coalesce(net_secondary,0))/10^6)::numeric, 3) as net_secondary_mn,
    substring(${mtd_dt_int}::varchar, 7, 2)::int day_no
from
    outlet
    left join
    base
    on
        outlet.retailer_qrcode = base.partner_qr_code
    and outlet.month_id = base.dt_id/100
where
    base.dt_id >= cast(to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD') + interval '-1 month'), 'YYYYMMDD') as integer)
and cast(substr(base.dt_id::varchar, 7, 2) as int) <= cast(substr(${mtd_dt_int}::varchar, 7, 2) as int)
group by 1,2,4,5,6,7

UNION ALL

select
    outlet.month_id,
    'FM' period,
    '3ID' brand,
    circle_ circle,
    region,
    se_category flag,
    sdp dist_id,
    max(base.dt_id) asof_date,
	sum(coalesce(ga,0)) ga,
	round((sum(coalesce(net_secondary,0))/10^6)::numeric, 3) as net_secondary_mn,
	cast(to_char(to_date(outlet.month_id::text || '01', 'yyyyMMdd') + interval '1 month' - interval '1 day', 'dd') as int) day_no
from
    outlet
    left join
    base
    on
        outlet.retailer_qrcode = base.partner_qr_code
    and outlet.month_id = base.dt_id/100
where
    base.dt_id < cast(to_char(date_trunc('month',to_date(${mtd_dt_int}::text, 'YYYYMMDD')), 'YYYYMMDD') as integer)
group by 1,4,5,6,7,11