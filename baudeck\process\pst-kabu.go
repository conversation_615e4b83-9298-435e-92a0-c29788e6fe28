package process

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"io/fs"
	"os"
	"slices"
	"strconv"
	"strings"
	"time"

	cfg "github.com/csee-pm/etl/baudeck/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
)

type PstKabuProcess struct {
	procFS fs.ReadFileFS
}

func NewPstKabuProcess(procFS fs.ReadFileFS) PstKabuProcess {
	return PstKabuProcess{procFS}
}

func (pst PstKabuProcess) GetReportData(c context.Context, workDate time.Time) (PstKabuReport, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)
	workDir := ctx.ExtractWorkDir(c)

	var err error
	if conf.Get("etl.work_date") != nil {
		mtdDate := conf.GetString("etl.work_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return PstKabuReport{}, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	conf.Set("work_date", workDate)

	var data []PstKabuData
	if cfg.UsePstKabuFromFile != "" {
		data, err = pst.getPstDataFromFile(c, cfg.UsePstKabuFromFile)
	} else {
		data, err = pst.getPstData(c, workDate)
	}

	if err != nil {
		return PstKabuReport{}, err
	}

	csvFilePath := fmt.Sprintf("%s/pst_kabu_%s.csv", workDir, time.Now().Format("20060102150405"))

	if cfg.UsePstKabuFromFile == "" {
		if err := utils.WriteToCsv(csvFilePath, data); err != nil {
			logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
		} else {
			logger.Info("CSV file written", "path", csvFilePath)
		}
	}

	return pst.postProcessData(data)
}

func (pst PstKabuProcess) getPstDataFromFile(c context.Context, fpath string) ([]PstKabuData, error) {
	logger := ctx.ExtractLogger(c)

	f, err := os.Open(fpath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read()

	var data []PstKabuData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}

		secondary, err := strconv.ParseFloat(record[8], 64)
		if err != nil {
			secondary = 0
			if record[8] != "" {
				logger.Error("failed to parse secondary value from file", "error", err, "value", record[8], "file", fpath)
			}
		}

		tertiary, err := strconv.ParseFloat(record[9], 64)
		if err != nil {
			tertiary = 0
			if record[9] != "" {
				logger.Error("failed to parse tertiary value from file", "error", err, "value", record[9], "file", fpath)
			}
		}

		data = append(data, PstKabuData{
			MonthID:   record[0],
			Period:    record[1],
			AsofDate:  record[2],
			Circle:    null.StringFrom(record[3]),
			Region:    null.StringFrom(record[4]),
			Kabupaten: null.StringFrom(record[5]),
			KabuFlag:  null.StringFrom(record[6]),
			Brand:     record[7],
			PstKabuKpi: PstKabuKpi{
				Secondary: null.FloatFrom(secondary),
				Tertiary:  null.FloatFrom(tertiary),
			},
		})
	}

	return data, nil
}

func (pst PstKabuProcess) getPstData(c context.Context, mtdDate time.Time) ([]PstKabuData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := pst.procFS.ReadFile("files/cwn_pst_kabu.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format(utils.ToGoDateFormat("YYYYMMDD"))},
	}

	kabumap, err := etlProc.GetKabuMap(c)
	if err != nil {
		return nil, err
	}

	logger.Info("Getting PST Kabu data")
	data, err := etlProc.QueryImpalaData[PstKabuData](c, string(buf), params)
	if err != nil {
		return nil, err
	}

	for i := range data {
		kabu := strings.TrimSpace(strings.ToUpper(data[i].Kabupaten.String))
		if segment, ok := kabumap[kabu]; ok {
			data[i].KabuFlag = null.StringFrom(segment)
		}
	}

	return data, nil
}

func (pst PstKabuProcess) postProcessData(data []PstKabuData) (PstKabuReport, error) {
	regionMap := make(map[string]*RegionalPstKabuData)
	circleMap := make(map[string]*RegionalPstKabuData)
	regionalKabuMap := make(map[string]map[string]*RegionalPstKabuData)
	nationalData := &RegionalPstKabuData{
		EntityType: "NATIONAL",
		EntityName: "INDONESIA",
		MTD:        &PstKabuKpi{},
		LMTD:       &PstKabuKpi{},
		FM:         make(map[string]*PstKabuKpi),
	}

	nationalKabuMap := make(map[string]*RegionalPstKabuData)

	var asofDate = ""

	fmMap := make(map[string]struct{})
	for _, d := range data {
		monthID := d.MonthID
		period := d.Period
		flag := strings.TrimSpace(strings.ToUpper(d.KabuFlag.String))
		circle := d.Circle.String
		region := d.Region.String

		if _, ok := regionMap[region]; !ok {
			regionMap[region] = &RegionalPstKabuData{
				EntityType: "REGION",
				EntityName: region,
				MTD:        &PstKabuKpi{Secondary: null.FloatFrom(0), Tertiary: null.FloatFrom(0)},
				LMTD:       &PstKabuKpi{Secondary: null.FloatFrom(0), Tertiary: null.FloatFrom(0)},
				FM:         make(map[string]*PstKabuKpi),
				KabuCount:  0,
			}

			regionalKabuMap[region] = make(map[string]*RegionalPstKabuData)
		}
		regionData := regionMap[region]

		if _, ok := regionalKabuMap[region][flag]; !ok {
			regionalKabuMap[region][flag] = &RegionalPstKabuData{
				EntityType: "FLAG",
				EntityName: flag,
				MTD:        &PstKabuKpi{Secondary: null.FloatFrom(0), Tertiary: null.FloatFrom(0)},
				LMTD:       &PstKabuKpi{Secondary: null.FloatFrom(0), Tertiary: null.FloatFrom(0)},
				FM:         make(map[string]*PstKabuKpi),
				KabuCount:  0,
			}
		}
		regionKabuData := regionalKabuMap[region][flag]

		if _, ok := circleMap[circle]; !ok {
			circleMap[circle] = &RegionalPstKabuData{
				EntityType: "CIRCLE",
				EntityName: circle,
				MTD:        &PstKabuKpi{Secondary: null.FloatFrom(0), Tertiary: null.FloatFrom(0)},
				LMTD:       &PstKabuKpi{Secondary: null.FloatFrom(0), Tertiary: null.FloatFrom(0)},
				FM:         make(map[string]*PstKabuKpi),
				KabuCount:  0,
			}
		}
		circleData := circleMap[circle]

		if _, ok := nationalKabuMap[flag]; !ok {
			nationalKabuMap[flag] = &RegionalPstKabuData{
				EntityType: "FLAG",
				EntityName: flag,
				MTD:        &PstKabuKpi{Secondary: null.FloatFrom(0), Tertiary: null.FloatFrom(0)},
				LMTD:       &PstKabuKpi{Secondary: null.FloatFrom(0), Tertiary: null.FloatFrom(0)},
				FM:         make(map[string]*PstKabuKpi),
				KabuCount:  0,
			}
		}
		nationalKabuData := nationalKabuMap[flag]

		switch period {
		case "MTD":
			asofDate = d.AsofDate
			regionData.MTD.Secondary.Float64 += d.Secondary.Float64
			regionData.MTD.Tertiary.Float64 += d.Tertiary.Float64
			regionData.KabuCount++

			regionKabuData.MTD.Secondary = d.Secondary
			regionKabuData.MTD.Tertiary = d.Tertiary
			regionKabuData.KabuCount++

			circleData.MTD.Secondary.Float64 += d.Secondary.Float64
			circleData.MTD.Tertiary.Float64 += d.Tertiary.Float64
			circleData.KabuCount++

			nationalData.MTD.Secondary.Float64 += d.Secondary.Float64
			nationalData.MTD.Tertiary.Float64 += d.Tertiary.Float64
			nationalData.KabuCount++

			nationalKabuData.MTD.Secondary.Float64 += d.Secondary.Float64
			nationalKabuData.MTD.Tertiary.Float64 += d.Tertiary.Float64
			nationalKabuData.KabuCount++

		case "LMTD":
			regionData.LMTD.Secondary.Float64 += d.Secondary.Float64
			regionData.LMTD.Tertiary.Float64 += d.Tertiary.Float64

			regionKabuData.LMTD.Secondary = d.Secondary
			regionKabuData.LMTD.Tertiary = d.Tertiary

			circleData.LMTD.Secondary.Float64 += d.Secondary.Float64
			circleData.LMTD.Tertiary.Float64 += d.Tertiary.Float64

			nationalData.LMTD.Secondary.Float64 += d.Secondary.Float64
			nationalData.LMTD.Tertiary.Float64 += d.Tertiary.Float64

			nationalKabuData.LMTD.Secondary.Float64 += d.Secondary.Float64
			nationalKabuData.LMTD.Tertiary.Float64 += d.Tertiary.Float64

		case "FM":
			fmMap[monthID] = struct{}{}
			if _, ok := regionData.FM[monthID]; !ok {
				regionData.FM[monthID] = &PstKabuKpi{null.FloatFrom(0), null.FloatFrom(0)}
			}
			regionData.FM[monthID].Secondary.Float64 += d.Secondary.Float64
			regionData.FM[monthID].Tertiary.Float64 += d.Tertiary.Float64

			if _, ok := regionKabuData.FM[monthID]; !ok {
				regionKabuData.FM[monthID] = &PstKabuKpi{null.FloatFrom(0), null.FloatFrom(0)}
			}
			regionKabuData.FM[monthID].Secondary = d.Secondary
			regionKabuData.FM[monthID].Tertiary = d.Tertiary

			if _, ok := circleData.FM[monthID]; !ok {
				circleData.FM[monthID] = &PstKabuKpi{null.FloatFrom(0), null.FloatFrom(0)}
			}
			circleData.FM[monthID].Secondary.Float64 += d.Secondary.Float64
			circleData.FM[monthID].Tertiary.Float64 += d.Tertiary.Float64

			if _, ok := nationalData.FM[monthID]; !ok {
				nationalData.FM[monthID] = &PstKabuKpi{null.FloatFrom(0), null.FloatFrom(0)}
			}
			nationalData.FM[monthID].Secondary.Float64 += d.Secondary.Float64
			nationalData.FM[monthID].Tertiary.Float64 += d.Tertiary.Float64

			if _, ok := nationalKabuData.FM[monthID]; !ok {
				nationalKabuData.FM[monthID] = &PstKabuKpi{null.FloatFrom(0), null.FloatFrom(0)}
			}
			nationalKabuData.FM[monthID].Secondary.Float64 += d.Secondary.Float64
			nationalKabuData.FM[monthID].Tertiary.Float64 += d.Tertiary.Float64
		}

	}

	fmList := utils.MapToList(fmMap, func(key string, value struct{}) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 3 {
		fmList = fmList[:3]
	}

	return PstKabuReport{
		AsofDate:        asofDate,
		FmList:          fmList,
		CircleMap:       circleMap,
		RegionalMap:     regionMap,
		RegionalKabuMap: regionalKabuMap,
		NationalData:    nationalData,
		NationalKabuMap: nationalKabuMap,
	}, nil
}

var (
	PST_SHEET = "to ppt"

	PSTKabuStartCol = 13

	PSTMtdOffsetRow  = 4
	PSTLmtdOffsetCol = 3

	kabuFlagMap = map[string]string{
		"MUST WIN 50":     "MUST WIN 50",
		"SUPER 88":        "SUPER 88",
		"MUST WIN - REST": "REST MUST WIN",
		"WINNING":         "WINNING",
	}
)

func (pst PstKabuProcess) WriteReport(xl *excelize.File, data PstKabuReport) error {
	startCells := map[string]xlutil.CellStruct{
		"WINNING":       xlutil.Cell(6, 13),
		"MUST WIN 50":   xlutil.Cell(16, 13),
		"SUPER 88":      xlutil.Cell(36, 13),
		"REST MUST WIN": xlutil.Cell(26, 13),
	}

	for k, c := range startCells {
		if err := pst.writePstKabuReport(xl, data.NationalKabuMap[k], c.Row, c.Col); err != nil {
			return err
		}
	}

	return nil
}

func (pst PstKabuProcess) writePstKabuReport(xl *excelize.File, data *RegionalPstKabuData, startRow, startCol int) error {
	shName := PST_SHEET

	fmList := utils.MapToList(data.FM, func(key string, value *PstKabuKpi) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 3 {
		fmList = fmList[:3]
	}

	for m, mth := range fmList {
		if m > 2 {
			break
		}

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		r := startRow + (2 - m)

		mthName := mthDate.Format("1/2/2006")
		if err := xl.SetCellValue(shName, xlutil.Cell(r, PSTKabuStartCol-1).Address(), mthName); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, PSTKabuStartCol).Address(), data.FM[mth].Secondary.Float64); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, PSTKabuStartCol+1).Address(), data.FM[mth].Tertiary.Float64); err != nil {
			return err
		}

	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+PSTMtdOffsetRow, PSTKabuStartCol).Address(), data.MTD.Secondary.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+PSTMtdOffsetRow, PSTKabuStartCol+1).Address(), data.MTD.Tertiary.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+PSTLmtdOffsetCol, PSTKabuStartCol).Address(), data.LMTD.Secondary.Float64); err != nil {
		return err
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(startRow+PSTLmtdOffsetCol, PSTKabuStartCol+1).Address(), data.LMTD.Tertiary.Float64); err != nil {
		return err
	}

	return nil
}
