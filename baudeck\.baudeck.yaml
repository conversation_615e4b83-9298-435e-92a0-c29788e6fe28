bigquery:
    json_key: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    project_id: data-commstrexe-prd-565x
email_server:
    credential: ZW5jLzE1OlZpdk5CRjBZQ3IxOFM5RnpoQjhIZ2xNdDd4OHcvbDFTOW15T3F4MEVQRmhQVDh0VTJYNkhIZFFYeGphdDRBRzVjZz09
    host: ************
    port: 25
    sender_address: <EMAIL>
    sender_name: CSEE Admin
error_notif_recipients:
    - <EMAIL>
gpfat:
    credential: ZW5jLzIxOlRNWTZiZTMzUnA4TTYwb1I4VTNFVm5rcGIrdlR2bFZaKzl3bUlQbFNhMUU5VExoMnJOWm1CazNEYWxFa2UwcDNoSXkzM1N3PQ==
    database: pdwh
    host: localhost
    port: 2345
impala:
    ca_cert: truststore.pem
    credential: ZW5jLzE3OjVHWU5FQ1ljcGgrcHlWTysvSVAwUVcvT2wrNTJ2S3ZpcVBtODlUMTBxcU9LV3F2alpvS0FFYkNjZCtvTTI2UCtCQT09
    database: default
    host: udc2-impala-lb.office.corp.indosat.com
    port: 21051
impala_landing_ssh:
    host: *************
    port: 22
    private_key_file: C:\Users\<USER>\.ssh\id_rsa
    user: "********"
log_level: 0
report_recipients:
    default:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
    topkpi:
        - <EMAIL>
root_dir: C:\Users\<USER>\Projects\go\csee\etl\bpcwn
tunnel:
    destination: ************:5432
    local_port: 2345
    ssh:
        host: ************
        port: 22
        private_key_file: C:\Users\<USER>\.ssh\id_rsa
        user: "********"
work_dir: workdir
