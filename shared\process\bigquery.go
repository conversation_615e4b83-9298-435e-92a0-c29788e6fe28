package process

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"cloud.google.com/go/auth/credentials"
	"cloud.google.com/go/bigquery"
	"google.golang.org/api/iterator"

	cfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	"github.com/likearthian/go-crypto"
)

type bqOption struct {
	ctx    context.Context
	params map[string]*etlDb.ParamValue
}

type BqOption func(o *bqOption)

func WithBqParams(params map[string]*etlDb.ParamValue) BqOption {
	return func(o *bqOption) {
		o.params = params
	}
}

func WithBqContext(ctx context.Context) BqOption {
	return func(o *bqOption) {
		o.ctx = ctx
	}
}

func QueryBigQueryData[T any](c context.Context, query string, options ...BqOption) ([]T, error) {
	logger := ctx.ExtractLogger(c)
	conf := ctx.ExtractConfig(c)
	bqConfig, err := cfg.GetConfig[cfg.BigQueryConfig](conf, "bigquery")
	if err != nil {
		return nil, err
	}

	detectOptions := &credentials.DetectOptions{
		Scopes: []string{bigquery.Scope},
	}

	if bqConfig.JsonKey != "" {
		logger.Debug("using json key for bigquery")
		jsonKey, err := crypto.DecodeBASE64(bqConfig.JsonKey)
		if err != nil {
			return nil, err
		}

		detectOptions.CredentialsJSON = jsonKey
	}

	creds, err := credentials.DetectDefault(detectOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to detect credentials: %w", err)
	}

	bq, err := etlDb.NewBigQuery(bqConfig.ProjectID, creds)
	if err != nil {
		return nil, err
	}

	defer bq.Close()

	var data []T
	if err := bqSelect(bq.Client, &data, query, options...); err != nil {
		return nil, err
	}

	return data, nil
}

func bqInjectQueryParams(qry string, params map[string]*etlDb.ParamValue) (string, error) {
	for k := range params {
		key := fmt.Sprintf("${%s}", k)
		repl := fmt.Sprintf("@%s", k)
		qry = strings.ReplaceAll(qry, key, repl)
	}

	return qry, nil
}

func bqCreateQuery(bq *bigquery.Client, qry string, params map[string]*etlDb.ParamValue) (*bigquery.Query, error) {
	qry, err := bqInjectQueryParams(qry, params)
	if err != nil {
		return nil, err
	}

	bquery := bq.Query(qry)
	if len(params) > 0 {
		var bparams []bigquery.QueryParameter
		for k, v := range params {
			bparams = append(bparams, bigquery.QueryParameter{Name: k, Value: v.Value})
		}
		bquery.Parameters = bparams
	}

	return bquery, nil
}

func bqSelect[T any](bq *bigquery.Client, dest *[]T, qry string, options ...BqOption) error {
	opt := bqOption{ctx: context.Background()}

	for _, o := range options {
		o(&opt)
	}

	if opt.ctx == nil {
		opt.ctx = context.Background()
	}

	query := bq.Query(qry)
	var err error
	if opt.params != nil {
		query, err = bqCreateQuery(bq, qry, opt.params)
		if err != nil {
			return err
		}
	}

	iter, err := query.Read(opt.ctx)
	if err != nil {
		return fmt.Errorf("failed to read query result: %w", err)
	}

	var columns []string
	for {
		var row T
		err := iter.Next(&row)
		if errors.Is(err, iterator.Done) {
			break
		}
		if err != nil {
			return err
		}

		if len(columns) == 0 {
			for _, col := range iter.Schema {
				columns = append(columns, col.Name)
			}
		}

		*dest = append(*dest, row)
	}

	return nil
}
