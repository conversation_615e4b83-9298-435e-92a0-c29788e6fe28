package pst_kabu

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"io/fs"
	"os"
	"slices"
	"strconv"
	"strings"
	"time"

	cfg "github.com/csee-pm/etl/bpcwn/config"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
)

type PstKabuProcess struct {
	procFS fs.ReadFileFS
}

func NewPstKabuProcess(procFS fs.ReadFileFS) PstKabuProcess {
	return PstKabuProcess{procFS}
}

func (pst PstKabuProcess) GetReportData(c context.Context, workDate time.Time) (PstKabuReport, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)
	workDir := ctx.ExtractWorkDir(c)

	var err error
	if conf.Get("etl.work_date") != nil {
		mtdDate := conf.GetString("etl.work_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return PstKabuReport{}, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	conf.Set("work_date", workDate)

	var data []PstKabuData
	if cfg.UsePstKabuFromFile != "" {
		data, err = pst.getPstDataFromFile(cfg.UsePstKabuFromFile)
	} else {
		data, err = pst.getPstData(c, workDate)
	}

	if err != nil {
		return PstKabuReport{}, err
	}

	csvFilePath := fmt.Sprintf("%s/pst_kabu_%s.csv", workDir, time.Now().Format("20060102150405"))

	if cfg.UsePstKabuFromFile == "" {
		if err := utils.WriteToCsv(csvFilePath, data); err != nil {
			logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
		} else {
			logger.Info("CSV file written", "path", csvFilePath)
		}
	}

	return pst.postProcessData(data)
}

func (pst PstKabuProcess) getPstDataFromFile(fpath string) ([]PstKabuData, error) {
	f, err := os.Open(fpath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read()

	var data []PstKabuData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}

		secondary, err := strconv.ParseFloat(record[8], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse secondary. %s", err)
		}

		tertiary, err := strconv.ParseFloat(record[9], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse tertiary. %s", err)
		}
		kabuCount, err := strconv.Atoi(record[7])
		if err != nil {
			return nil, fmt.Errorf("failed to parse kabu count. %s", err)
		}

		data = append(data, PstKabuData{
			MonthID:   record[0],
			Period:    record[1],
			AsofDate:  record[2],
			Circle:    null.StringFrom(record[3]),
			Region:    null.StringFrom(record[4]),
			KabuFlag:  null.StringFrom(record[5]),
			Brand:     record[6],
			KabuCount: kabuCount,
			PstKabuKpi: PstKabuKpi{
				SecondaryMn: null.FloatFrom(secondary),
				TertiaryMn:  null.FloatFrom(tertiary),
			},
		})
	}

	return data, nil
}

func (pst PstKabuProcess) getPstData(c context.Context, mtdDate time.Time) ([]PstKabuData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := pst.procFS.ReadFile("files/cwn_pst_kabu.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format(utils.ToGoDateFormat("YYYYMMDD"))},
	}

	kabumap, err := etlProc.GetKabuMap(c)
	if err != nil {
		return nil, err
	}

	logger.Info("Getting PST Kabu data")
	data, err := etlProc.QueryImpalaData[PstKabuData](c, string(buf), params)
	if err != nil {
		return nil, err
	}

	for i := range data {
		kabu := strings.TrimSpace(strings.ToUpper(data[i].Kabupaten.String))
		if segment, ok := kabumap[kabu]; ok {
			data[i].KabuFlag = null.StringFrom(segment)
		}
	}

	return data, nil
}

func (pst PstKabuProcess) postProcessData(data []PstKabuData) (PstKabuReport, error) {
	regionMap := make(map[string]*RegionalPstKabuData)
	circleMap := make(map[string]*RegionalPstKabuData)
	regionalKabuMap := make(map[string]map[string]*RegionalPstKabuData)
	nationalData := &RegionalPstKabuData{
		EntityType: "NATIONAL",
		EntityName: "INDONESIA",
		MTD:        &PstKabuKpi{},
		LMTD:       &PstKabuKpi{},
		FM:         make(map[string]*PstKabuKpi),
	}

	nationalKabuMap := make(map[string]*RegionalPstKabuData)

	var asofDate = ""

	fmMap := make(map[string]struct{})
	for _, d := range data {
		monthID := d.MonthID
		period := d.Period
		flag := strings.TrimSpace(strings.ToUpper(d.KabuFlag.String))
		circle := d.Circle.String
		region := d.Region.String

		if _, ok := regionMap[region]; !ok {
			regionMap[region] = &RegionalPstKabuData{
				EntityType: "REGION",
				EntityName: region,
				MTD:        &PstKabuKpi{SecondaryMn: null.FloatFrom(0), TertiaryMn: null.FloatFrom(0)},
				LMTD:       &PstKabuKpi{SecondaryMn: null.FloatFrom(0), TertiaryMn: null.FloatFrom(0)},
				FM:         make(map[string]*PstKabuKpi),
			}

			regionalKabuMap[region] = make(map[string]*RegionalPstKabuData)
		}
		regionData := regionMap[region]

		if _, ok := regionalKabuMap[region][flag]; !ok {
			regionalKabuMap[region][flag] = &RegionalPstKabuData{
				EntityType: "FLAG",
				EntityName: flag,
				MTD:        &PstKabuKpi{SecondaryMn: null.FloatFrom(0), TertiaryMn: null.FloatFrom(0)},
				LMTD:       &PstKabuKpi{SecondaryMn: null.FloatFrom(0), TertiaryMn: null.FloatFrom(0)},
				FM:         make(map[string]*PstKabuKpi),
			}
		}
		regionKabuData := regionalKabuMap[region][flag]

		if _, ok := circleMap[circle]; !ok {
			circleMap[circle] = &RegionalPstKabuData{
				EntityType: "CIRCLE",
				EntityName: circle,
				MTD:        &PstKabuKpi{SecondaryMn: null.FloatFrom(0), TertiaryMn: null.FloatFrom(0)},
				LMTD:       &PstKabuKpi{SecondaryMn: null.FloatFrom(0), TertiaryMn: null.FloatFrom(0)},
				FM:         make(map[string]*PstKabuKpi),
			}
		}
		circleData := circleMap[circle]

		if _, ok := nationalKabuMap[flag]; !ok {
			nationalKabuMap[flag] = &RegionalPstKabuData{
				EntityType: "FLAG",
				EntityName: flag,
				MTD:        &PstKabuKpi{SecondaryMn: null.FloatFrom(0), TertiaryMn: null.FloatFrom(0)},
				LMTD:       &PstKabuKpi{SecondaryMn: null.FloatFrom(0), TertiaryMn: null.FloatFrom(0)},
				FM:         make(map[string]*PstKabuKpi),
			}
		}
		nationalKabuData := nationalKabuMap[flag]

		switch period {
		case "MTD":
			asofDate = d.AsofDate
			regionData.MTD.SecondaryMn.Float64 += d.SecondaryMn.Float64
			regionData.MTD.TertiaryMn.Float64 += d.TertiaryMn.Float64
			regionData.KabuCount += d.KabuCount

			regionKabuData.MTD.SecondaryMn = d.SecondaryMn
			regionKabuData.MTD.TertiaryMn = d.TertiaryMn
			regionKabuData.KabuCount = d.KabuCount

			circleData.MTD.SecondaryMn.Float64 += d.SecondaryMn.Float64
			circleData.MTD.TertiaryMn.Float64 += d.TertiaryMn.Float64
			circleData.KabuCount += d.KabuCount

			nationalData.MTD.SecondaryMn.Float64 += d.SecondaryMn.Float64
			nationalData.MTD.TertiaryMn.Float64 += d.TertiaryMn.Float64
			nationalData.KabuCount += d.KabuCount

			nationalKabuData.MTD.SecondaryMn.Float64 += d.SecondaryMn.Float64
			nationalKabuData.MTD.TertiaryMn.Float64 += d.TertiaryMn.Float64
			nationalKabuData.KabuCount += d.KabuCount

		case "LMTD":
			regionData.LMTD.SecondaryMn.Float64 += d.SecondaryMn.Float64
			regionData.LMTD.TertiaryMn.Float64 += d.TertiaryMn.Float64

			regionKabuData.LMTD.SecondaryMn = d.SecondaryMn
			regionKabuData.LMTD.TertiaryMn = d.TertiaryMn

			circleData.LMTD.SecondaryMn.Float64 += d.SecondaryMn.Float64
			circleData.LMTD.TertiaryMn.Float64 += d.TertiaryMn.Float64

			nationalData.LMTD.SecondaryMn.Float64 += d.SecondaryMn.Float64
			nationalData.LMTD.TertiaryMn.Float64 += d.TertiaryMn.Float64

			nationalKabuData.LMTD.SecondaryMn.Float64 += d.SecondaryMn.Float64
			nationalKabuData.LMTD.TertiaryMn.Float64 += d.TertiaryMn.Float64

		case "FM":
			fmMap[monthID] = struct{}{}
			if _, ok := regionData.FM[monthID]; !ok {
				regionData.FM[monthID] = &PstKabuKpi{null.FloatFrom(0), null.FloatFrom(0)}
			}
			regionData.FM[monthID].SecondaryMn.Float64 += d.SecondaryMn.Float64
			regionData.FM[monthID].TertiaryMn.Float64 += d.TertiaryMn.Float64

			if _, ok := regionKabuData.FM[monthID]; !ok {
				regionKabuData.FM[monthID] = &PstKabuKpi{null.FloatFrom(0), null.FloatFrom(0)}
			}
			regionKabuData.FM[monthID].SecondaryMn = d.SecondaryMn
			regionKabuData.FM[monthID].TertiaryMn = d.TertiaryMn

			if _, ok := circleData.FM[monthID]; !ok {
				circleData.FM[monthID] = &PstKabuKpi{null.FloatFrom(0), null.FloatFrom(0)}
			}
			circleData.FM[monthID].SecondaryMn.Float64 += d.SecondaryMn.Float64
			circleData.FM[monthID].TertiaryMn.Float64 += d.TertiaryMn.Float64

			if _, ok := nationalData.FM[monthID]; !ok {
				nationalData.FM[monthID] = &PstKabuKpi{null.FloatFrom(0), null.FloatFrom(0)}
			}
			nationalData.FM[monthID].SecondaryMn.Float64 += d.SecondaryMn.Float64
			nationalData.FM[monthID].TertiaryMn.Float64 += d.TertiaryMn.Float64

			if _, ok := nationalKabuData.FM[monthID]; !ok {
				nationalKabuData.FM[monthID] = &PstKabuKpi{null.FloatFrom(0), null.FloatFrom(0)}
			}
			nationalKabuData.FM[monthID].SecondaryMn.Float64 += d.SecondaryMn.Float64
			nationalKabuData.FM[monthID].TertiaryMn.Float64 += d.TertiaryMn.Float64
		}

	}

	fmList := utils.MapToList(fmMap, func(key string, value struct{}) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 3 {
		fmList = fmList[:3]
	}

	return PstKabuReport{
		AsofDate:        asofDate,
		FmList:          fmList,
		CircleMap:       circleMap,
		RegionalMap:     regionMap,
		RegionalKabuMap: regionalKabuMap,
		NationalData:    nationalData,
		NationalKabuMap: nationalKabuMap,
	}, nil
}

var (
	PST_SHEET = "Summary New SEA"

	SecStartCol = 7
	TerStartCol = 15

	RegionCol    = 3
	FlagCol      = 4
	TotalKabuCol = 5

	mtdOffsetCol    = 4
	lmtdOffsetCol   = 3
	absOffsetCol    = 5
	growthOffsetCol = 6

	kabuFlagMap = map[string]string{
		"MUST WIN 50":     "MUST WIN 50",
		"SUPER 88":        "SUPER 88",
		"MUST WIN - REST": "REST MUST WIN",
		"WINNING":         "WINNING",
	}
)

func (pst PstKabuProcess) WriteReport(xl *excelize.File, data PstKabuReport) error {
	startRow := 6
	endRow := 66

	asofDateStr := ""
	asofDate, err := time.Parse("20060102", data.AsofDate)
	if err == nil {
		asofDateStr = asofDate.Format("02-Jan-2006")
	}

	xl.SetCellStr(PST_SHEET, "C2", fmt.Sprintf("data as of: %s", asofDateStr))

	if err := pst.writePstKabuReport(xl, data, startRow, endRow); err != nil {
		return err
	}

	return nil
}

func (pst PstKabuProcess) writePstKabuReport(xl *excelize.File, data PstKabuReport, startRow, endRow int) error {
	shName := PST_SHEET

	circle := ""
	region := ""

	fmList := data.FmList

	for m, mth := range fmList {
		if m > 2 {
			break
		}

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		mthName := mthDate.Format("Jan-06")
		if err := xl.SetCellValue(shName, xlutil.Cell(startRow-1, SecStartCol+(2-m)).Address(), mthName); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(startRow-1, TerStartCol+(2-m)).Address(), mthName); err != nil {
			return err
		}
	}

	for r := startRow; r <= endRow; r++ {
		kabuFlag := ""

		entity, err := xl.GetCellValue(shName, xlutil.Cell(r, RegionCol).Address())
		if err != nil {
			return err
		}
		entity = strings.TrimSpace(entity)

		if entity == "JAYA" {
			entity = "JAKARTA RAYA"
		}

		kabuFlag, err = xl.GetCellValue(shName, xlutil.Cell(r, FlagCol).Address())
		if err != nil {
			return err
		}
		kabuFlag = strings.TrimSpace(strings.ToUpper(kabuFlag))

		kabuFlag = kabuFlagMap[kabuFlag]

		var regData *RegionalPstKabuData
		if _, ok := data.CircleMap[entity]; ok {
			regData = data.CircleMap[entity]
			circle = entity
		}

		if _, ok := data.RegionalMap[entity]; ok {
			regData = data.RegionalMap[entity]
			region = entity
		}

		if entity == "TOTAL NATIONAL" {
			regData = data.NationalData
			circle = ""
			region = ""
		}

		if kabuFlag != "" {
			var exists bool
			var regKabuData map[string]*RegionalPstKabuData
			if circle == "" && region == "" {
				regData, exists = data.NationalKabuMap[kabuFlag]
			} else {
				regKabuData, exists = data.RegionalKabuMap[region]
				if exists {
					regData, exists = regKabuData[kabuFlag]
				}
			}
		}

		if regData == nil {
			continue
		}

		//fmt.Printf("entity: %s, circle: %s, region: %s, kabuFlag: %s, kabuCount: %d\n", entity, circle, region, kabuFlag, regData.KabuCount)

		secAbs := (regData.MTD.SecondaryMn.Float64 - regData.LMTD.SecondaryMn.Float64) / 1000
		secGrowth := (regData.MTD.SecondaryMn.Float64 - regData.LMTD.SecondaryMn.Float64) / regData.LMTD.SecondaryMn.Float64

		terAbs := (regData.MTD.TertiaryMn.Float64 - regData.LMTD.TertiaryMn.Float64) / 1000
		terGrowth := (regData.MTD.TertiaryMn.Float64 - regData.LMTD.TertiaryMn.Float64) / regData.LMTD.TertiaryMn.Float64

		if err := xl.SetCellValue(shName, xlutil.Cell(r, TotalKabuCol).Address(), regData.KabuCount); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, SecStartCol+mtdOffsetCol).Address(), regData.MTD.SecondaryMn.Float64/1000); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, SecStartCol+lmtdOffsetCol).Address(), regData.LMTD.SecondaryMn.Float64/1000); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, SecStartCol+absOffsetCol).Address(), secAbs); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, SecStartCol+growthOffsetCol).Address(), secGrowth); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, TerStartCol+mtdOffsetCol).Address(), regData.MTD.TertiaryMn.Float64/1000); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, TerStartCol+lmtdOffsetCol).Address(), regData.LMTD.TertiaryMn.Float64/1000); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, TerStartCol+absOffsetCol).Address(), terAbs); err != nil {
			return err
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(r, TerStartCol+growthOffsetCol).Address(), terGrowth); err != nil {
			return err
		}

		for m, mth := range fmList {
			if m > 2 {
				break
			}

			if _, ok := regData.FM[mth]; !ok {
				continue
			}

			fmData := regData.FM[mth]

			if err := xl.SetCellValue(shName, xlutil.Cell(r, SecStartCol+(2-m)).Address(), fmData.SecondaryMn.Float64/1000); err != nil {
				return err
			}

			if err := xl.SetCellValue(shName, xlutil.Cell(r, TerStartCol+(2-m)).Address(), fmData.TertiaryMn.Float64/1000); err != nil {
				return err
			}
		}

	}

	return nil
}
